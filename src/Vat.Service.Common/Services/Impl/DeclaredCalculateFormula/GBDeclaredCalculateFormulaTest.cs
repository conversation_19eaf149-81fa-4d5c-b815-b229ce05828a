using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using BFE.Framework.Infrastructure.Crosscutting.Constant;
using BFE.Framework.Infrastructure.Crosscutting.Helpers;
using Microsoft.Extensions.Logging;
using Vat.Service.Common.Model;
using Vat.Service.Common.Model.SaleAmountStatistics;
using Vat.Service.Domain.AggregatesModel.NationalBusinessAgg.Specifications;
using Vat.Service.Domain.AggregatesModel.VATExchangeRateAgg;
using Vat.Service.Domain.Repositories.NationalBusiness;

namespace Vat.Service.Common.Services.Impl.DeclaredCalculateFormula
{
    public class GBDeclaredCalculateFormulaTest : IDeclaredCalculateFormula
    {
        private readonly ILogger<GBDeclaredCalculateFormulaTest> _logger;
        private readonly string _countryCode = "GB";
        private readonly string _currencyCode = "GBP";
        private readonly decimal _taxrate = 0.2M;

        public GBDeclaredCalculateFormulaTest(ILogger<GBDeclaredCalculateFormulaTest> logger)
        {
            this._logger = logger;
        }

        public async Task<EBaySaleAmountStatistics> EBaySalesAmountCalculate(List<EBaySalesData> ebaySalesDatas, List<string> declareMonthList, List<VatExchangeRate> exchangeRateList)
        {
            //var nationalBusiness = await _nationalBusinessReadOnlyRepository.GetAsync(new MatchNationalBusinessByCountryCodeSpecification("GB"));
            var result = new EBaySaleAmountStatistics();

            ebaySalesDatas = ebaySalesDatas.Where(d => !d.SaleDate.IsNullOrBlank() && d.BuyerCountry == "United Kingdom" && d.ItemCountry == "GB").ToList();
            var ebayDeteilByMonthList = new List<EbaySaleAmountStatisticsItemDetailByMonth>();
            foreach (var declareMonth in declareMonthList)
            {
                var detailByMonth = GetEBayDataStatisticsDetail(ebaySalesDatas, declareMonth, exchangeRateList);
                ebayDeteilByMonthList.Add(detailByMonth);
            }
            result.EbaySaleAmountStatisticsItemDetailByMonths = ebayDeteilByMonthList;

            result.NormalSaleAmount = new Money(Math.Round(ebayDeteilByMonthList.Sum(p => p.Detail.LocalCurrencyStatisticalAmount.Amount + p.Detail.USDToGBPExchangeRate * p.Detail.USDStatisticalAmount.Amount), 2), _currencyCode);

            result.NormalSaleAmountAmtTax = new Money(Math.Round(ebayDeteilByMonthList.Sum(p => p.Detail.StatisticalTaxAmount.Amount + p.Detail.USDToGBPExchangeRate * p.Detail.USDStatisticalTaxAmount.Amount), 2), _currencyCode);

            return result;
        }
        private EbaySaleAmountStatisticsItemDetailByMonth GetEBayDataStatisticsDetail(List<EBaySalesData> ebaySalesDatas, string month, List<VatExchangeRate> exchangeRateList)
        {
            var detailByMonth = new EbaySaleAmountStatisticsItemDetailByMonth();
            //币种切换
            Dictionary<string, string> dicCurrency = new Dictionary<string, string>()
            {
                { "美元","US $"}
            };
            var monthNum = Convert.ToDateTime(month).ToString("yyyy-MM");
            bool beforeTaxReform = Convert.ToDateTime(month) < new DateTime(2021, 1, 1);
            var dicRate = exchangeRateList.Where(r => r.Month == monthNum).ToList().ToDictionary(c => dicCurrency[c.ExchangeCurrency], c => c.ExchangeRate); ;
            dicRate.Add("￡", 1);
            dicRate.Add("£", 1);
            dicRate.Add("GB ￡", 1);
            dicRate.Add("GB £", 1);
            var dicCurrencyCode = new Dictionary<string, string> { { "GB ￡", _currencyCode }, { "GB £", _currencyCode }, { "￡", _currencyCode }, { "£", _currencyCode }, { "US $", "USD" } };
            EbaySaleAmountStatisticsItemDetail detail = new EbaySaleAmountStatisticsItemDetail()
            {
                StatisticalPeriod = monthNum
            };
            var ebaySalesMonthDatas = ebaySalesDatas.Where(d => d.DispatchedMonth == month).ToList();
            detail.CurrencyItems = ebaySalesMonthDatas.GroupBy(d => d.CurrencyCode)
                .Select(d => new EbayCurrencyItem
                {
                    CurrencySalesAmount = new Money(Math.Round(d.Sum(ed => Convert.ToDecimal(ed.TotalPriceMoney)) / (1 + _taxrate), 2), dicCurrencyCode[d.Key]),
                    ExchangeRate = dicRate[d.Key],
                    CurrencyTaxAmount = new Money(Math.Round(d.Sum(ed => Convert.ToDecimal(ed.SellerCollectedTaxMoney.IsNullOrBlank() ? "0" : ed.SellerCollectedTaxMoney)), 2), dicCurrencyCode[d.Key]),
                    CurrencyCode = dicCurrencyCode[d.Key]
                }).ToList();
            //这里是没有除以汇率的
            detail.StatisticalTaxAmount = new Money(detail.CurrencyItems.Sum(d => Math.Round(d.CurrencyTaxAmount.Amount, 2)), _currencyCode);
            detail.LocalCurrencyStatisticalAmount = new Money(detail.CurrencyItems.Sum(d => Math.Round(d.CurrencySalesAmount.Amount, 2)), _currencyCode);
            detail.USDToGBPExchangeRate = exchangeRateList.FirstOrDefault(p => p.Month == monthNum).ExchangeRate;
            detail.GBPExchangeRate = 1;
            detail.USDStatisticalAmount = detail.CurrencyItems.FirstOrDefault(p => p.CurrencyCode == "US $") == null ? new Money(0, "USD") : new Money(detail.CurrencyItems.Where(p => p.CurrencyCode == "US $").Sum(d => Math.Round(d.CurrencySalesAmount.Amount, 2)), "USD");
            detail.USDStatisticalTaxAmount = detail.CurrencyItems.FirstOrDefault(p => p.CurrencyCode == "US $") == null ? new Money(0, "USD") : new Money(detail.CurrencyItems.Where(p => p.CurrencyCode == "US $").Sum(d => Math.Round(d.CurrencyTaxAmount.Amount, 2)), "USD");
            detailByMonth.DeclareMonth = month;
            detailByMonth.Detail = detail;
            return detailByMonth;
        }
        public void SetRateTime(DateTime start, DateTime end, DateTime effectiveTime)
        {

        }
        /// <summary>
        /// 申报税额计算
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public Dictionary<string, Money> VatPayableCalculate(VatDeclaredCalcModel model)
        {
            decimal rate = model.TaxRate;
            // 被代扣代缴净销售额
            var amazonSaleAmount = Math.Round(model.AmazonSaleAmount, 2);
            var amazonAmtTax = (model.AmazonAmtTax / 1.2M) * 0.2M;
            var sellerTaxAmount = model.SellerTaxAmount;
            // if (sellerTaxAmount != null && !string.IsNullOrEmpty(sellerTaxAmount.ToString()) && sellerTaxAmount != 0)
            // {
            //     // 未被代扣代缴销售额 - COMMINGLING_BUY的税金
            //     decimal result;
            //     decimal.TryParse(sellerTaxAmount.ToString(), out result);
            //     amazonAmtTax = amazonAmtTax - result;
            // }
            _logger.LogError($"box1的值：{amazonAmtTax}");
            // amazonAmtTax = Math.Round(amazonAmtTax, 2);
            var otherSaleTax = Math.Round(model.OtherSaleTax, 2);
            decimal otherdeclareAmount = Math.Round(model.OtherDeclaredSalesAmount, 2);
            var ebayTotalPriceAmount = Math.Round(model.EBayTotalPriceAmount, 2);
            //var ebaySoldForAmount = Math.Round(model.EBaySoldForAmount, 2);
            var ebaySellerCollectedTaxAmount = Math.Round(model.EBaySellerCollectedTaxAmount, 2);
            decimal importVatTaxAmount = 0;
            if (model.DeclareItems.ContainsKey("D0001"))
                importVatTaxAmount = Math.Round(model.DeclareItems["D0001"], 2);
            //var importVatTaxAmount = Math.Round(model.DeclareItems["D0001"], 2);//进口增值税
            decimal saleToEUAmount = 0;
            if (model.DeclareItems.ContainsKey("D0002"))
                saleToEUAmount = Math.Round(model.DeclareItems["D0002"], 2);
            //var saleToEUAmount = Math.Round(model.DeclareItems["D0002"], 2);//从英国销售到欧盟国家（不含英国）的免税总销售额
            decimal purchaseFromEUAmount = 0;
            if (model.DeclareItems.ContainsKey("D0003"))
                purchaseFromEUAmount = Math.Round(model.DeclareItems["D0003"], 2);
            //var purchaseFromEUAmount = Math.Round(model.DeclareItems["D0003"], 2);//从欧盟国家（不含英国）的不含税采购金额
            decimal gbPurchaseVat = 0;
            if (model.DeclareItems.ContainsKey("D0004"))
                gbPurchaseVat = Math.Round(model.DeclareItems["D0004"], 2);
            //var gbPurchaseVat = Math.Round(model.DeclareItems["D0004"], 2);//英国国内采购增值税
            decimal gbpva = 0;
            if (model.DeclareItems.ContainsKey("D0007"))//PVA递延
                gbpva = Math.Round(model.DeclareItems["D0007"], 2);

            var declareTotalAmount = Math.Round((model.AmazonAmtTax + otherdeclareAmount + ebayTotalPriceAmount + model.AmazonSaleAmount * 1.2M), 2);//含税销售总额
            //var exclDeclareTotalAmount = Math.Round(model.AmazonAmtTax + otherdeclareAmount + ebayTotalPriceAmount, 2);
            var salesVatAmount = Math.Round(amazonAmtTax + ebaySellerCollectedTaxAmount + otherSaleTax + gbpva, 2);//第1项

            var purchaseFromEUVatAmount = 0M; //Math.Round(purchaseFromEUAmount * rate, 2);//第2项
            var totalVatAmount = Math.Round(salesVatAmount + purchaseFromEUVatAmount, 2);//第3项
            var purcharsesVatAmount = importVatTaxAmount + purchaseFromEUVatAmount + gbPurchaseVat + gbpva;//第4项
            if (sellerTaxAmount != null && !string.IsNullOrEmpty(sellerTaxAmount.ToString()) && sellerTaxAmount != 0)
            {
                // +COMMINGLING_BUY的税金
                decimal result;
                decimal.TryParse(sellerTaxAmount.ToString(), out result);
                purcharsesVatAmount = purcharsesVatAmount + result;
            }
            purcharsesVatAmount = Math.Round(purcharsesVatAmount, 2);
            var reclaimedVatAmount = totalVatAmount - purcharsesVatAmount;//第5项
            var totalSalesAmount = amazonSaleAmount + ebayTotalPriceAmount + otherdeclareAmount + model.AmazonAmtTax / 1.2M;//第6项
            // 出口到非欧盟国家的销售额
            var exportToNonEUAmount = model.ExportToNonEUAmount;
            if (exportToNonEUAmount != null && !string.IsNullOrEmpty(exportToNonEUAmount.ToString()) && exportToNonEUAmount != 0)
            {
                decimal result;
                decimal.TryParse(exportToNonEUAmount.ToString(), out result);
                totalSalesAmount = totalSalesAmount + result;
            }
            // 出口到欧盟国家的销售额：有输入用输入的值，没有则取表格计算结果
            decimal exportToEUAmount = 0m;
            if (saleToEUAmount != 0)
            {
                exportToEUAmount = saleToEUAmount;
            }
            else
            {
                if (model.ExportToEUAmount != null && !string.IsNullOrEmpty(model.ExportToEUAmount.ToString()) && model.ExportToEUAmount != 0)
                {
                    decimal result;
                    decimal.TryParse(model.ExportToEUAmount.ToString(), out result);
                    exportToEUAmount = result;
                }
            }
            totalSalesAmount += exportToEUAmount;
            totalSalesAmount = Math.Round(totalSalesAmount, 2);
            var totalPurchasesAmount = Math.Round(purcharsesVatAmount / rate + purchaseFromEUAmount, 2);//第7项
            var totalSaleToEUAmount = Math.Round(exportToEUAmount, 2);//第8项
            var totalPurchaseFromEUAmount = Math.Round(purchaseFromEUAmount, 2);//第9项
            //var totalSalesAmountInclTax = Math.Round(declareTotalAmount + ebayTotalPriceAmount, 2);
            //var totalSalesAmountExclTax = Math.Round(model.AmazonDeclaredSalesAmountExclTax + otherDeclareSaleAmountLater + ebaySoldForAmount, 2);

            var data = new Dictionary<string, Money>() {
                { "SalesVatAmount",new Money( salesVatAmount,_currencyCode )},
                { "PurchaseFromEUVatAmount",new Money( purchaseFromEUVatAmount,_currencyCode )},
                { "TotalVatAmount",new Money( totalVatAmount,_currencyCode )},
                { "PurcharsesVatAmount",new Money( purcharsesVatAmount,_currencyCode )},
                { "ReclaimedVatAmount",new Money( reclaimedVatAmount,_currencyCode )},
                { "TotalSalesAmount",new Money( totalSalesAmount,_currencyCode )},
                { "TotalPurchasesAmount",new Money( totalPurchasesAmount,_currencyCode )},
                { "TotalSaleToEUAmount",new Money( totalSaleToEUAmount,_currencyCode )},
                { "TotalPurchaseFromEUAmount",new Money( totalPurchaseFromEUAmount,_currencyCode )},

                { "DeclaredTotalSalesAmount",new Money( declareTotalAmount,_currencyCode )},
                { "ImportVatTaxAmount",new Money( importVatTaxAmount,_currencyCode )},
                { "OtherDeduction",new Money( gbPurchaseVat,_currencyCode )},
                // 含税申报销售总额
                { "totalSalesAmountInclTax",new Money(Math.Round(totalSalesAmount * 1.2m, 2),_currencyCode)},
                // 不含税申报销售总额
                { "totalSalesAmountExclTax",new Money(totalSalesAmount,_currencyCode)}

            };
            return data;
        }
        /// <summary>
        /// 转换申报金额数据
        /// </summary>
        /// <param name="model"></param>
        /// <param name="deductionItems"></param>
        /// <returns></returns>
        public DeclaredDataModel ConvertToDeclaredData(VatDeclaredCalcModel model, List<DeductionItemModel> deductionItems)
        {
            var dic = VatPayableCalculate(model);
            var result = new DeclaredDataModel()
            {
                DeclaredConfirmItems = dic,
                DeductionItems = deductionItems,
                DeductionAmount = dic["OtherDeduction"],
                ImportVatTaxAmount = dic["ImportVatTaxAmount"],
                Interest = new Money(0, _currencyCode),
                SalesVatAmount = dic["SalesVatAmount"],
                TaxDue = dic["ReclaimedVatAmount"],
                TotalSalesAmount = dic["DeclaredTotalSalesAmount"],
                DeclareSaleAmountExclTax = dic["totalSalesAmountExclTax"],
                DeclareSaleAmountInclTax = dic["totalSalesAmountInclTax"]
            };
            return result;
        }

        public Task<AliexpressSaleAmountStatistics> AliexpressSalesAmountCalculate(List<AliexpressSalesData> customerSalesDatas, List<string> declareMonthList, List<string> euCountryList, List<VatExchangeRate> exchangeRateList, List<string> existVatCountryCodeList)
        {
            throw new NotImplementedException();
        }

        /// <summary>
        /// 2025年新规则亚马逊VAT税金计算
        /// </summary>
        /// <param name="customerSalesDatas"></param>
        /// <param name="declareMonthList"></param>
        /// <param name="euCountryList"></param>
        /// <param name="declareMonthDic"></param>
        /// <param name="arrivalCountryList"></param>
        /// <param name="exchangeRateList"></param>
        /// <param name="euCountryDic"></param>
        /// <param name="existVatCountryCodeList"></param>
        /// <returns></returns>
        public async Task<SaleAmountStatisticsResult> AmazonSalesAmountCalculate(List<CustomerSalesData> customerSalesDatas, List<string> declareMonthList, List<string> euCountryList,
           Dictionary<string, string> declareMonthDic, List<string> arrivalCountryList, List<VatExchangeRate> exchangeRateList, Dictionary<string, string> euCountryDic,
            List<string> existVatCountryCodeList)
        {
            var result = new SaleAmountStatisticsResult();
            var currencyDic = ConstantHelper.GetConstantInfoList(typeof(CurrencyCode)).ToDictionary(c => c.Value, c => c.Description);
            _logger.LogError($"计算月份：{string.Join(',', declareMonthList)}，所有欧盟国家：{string.Join(',', euCountryList)}");

            // 筛选申报期间的数据
            var salesDatas = customerSalesDatas.Where(c => declareMonthList.Contains(c.ACTIVITY_PERIOD) && c.TAX_COLLECTION_RESPONSIBILITY != null).ToList();

            decimal totalSellerAmount = 0; // 未被代扣代缴销售额
            decimal totalComminglingBuyAmount = 0; // "COMMINGLING_BUY"的税金
            decimal totalMarketplaceTaxAmount = 0; // 被代扣代缴净销售额
            decimal totalExportToEUAmount = 0; // 出口到欧盟国家的销售额
            decimal totalExportToNonEUAmount = 0; // 出口到非欧盟国家的销售额

            foreach (var declareMonth in declareMonthList)
            {
                var month = declareMonthDic[declareMonth];
                var monthSalesData = salesDatas.Where(c => c.ACTIVITY_PERIOD == declareMonth).ToList();

                // 1. 未被代扣的订单（卖家需要自行缴纳税金）
                var sellerResponsibilityData = monthSalesData.Where(c =>
                    c.TAX_COLLECTION_RESPONSIBILITY.ToLower() == "seller").ToList();

                // 第一步：计算未代扣代缴订单的含税销售额
                var netAmount = CalculateSellerTaxableAmount(sellerResponsibilityData, exchangeRateList, month);
                totalSellerAmount += Math.Round(netAmount, 2);
                // var step1Amount = Math.Round((netAmount / (1 + _taxrate)) * _taxrate, 2);
                // 第二步：计算"COMMINGLING_BUY"的税金
                var comminglingBuyTax = CalculateComminglingBuyTax(sellerResponsibilityData, exchangeRateList, month);

                // 第三步：计算卖家最终应缴纳税金
                // var sellerFinalTax = step1Amount - comminglingBuyTax;
                totalComminglingBuyAmount += comminglingBuyTax;
                _logger.LogError($"当前计算月份：{month}；当前月份未代扣代缴订单的含税销售额：{netAmount}；当前月份COMMINGLING_BUY的税金：{comminglingBuyTax}");

                // 2. 被代扣代缴净销售额（平台已经代扣代缴，只申报不交税）
                var marketplaceTaxAmount = CalculateMarketplaceTaxAmount(monthSalesData, exchangeRateList, month);
                totalMarketplaceTaxAmount += marketplaceTaxAmount;

                // 3. 出口到欧盟国家的销售额
                var exportToEUData = monthSalesData.Where(c =>
                    c.TAX_COLLECTION_RESPONSIBILITY.ToLower() == "seller" &&
                    c.SALE_DEPART_COUNTRY == _countryCode &&
                    euCountryList.Contains(c.SALE_ARRIVAL_COUNTRY)).ToList();

                var exportToEUAmount = CalculateExportAmount(exportToEUData, exchangeRateList, month);
                totalExportToEUAmount += exportToEUAmount;
                _logger.LogError($"当前计算月份：{month}；出口到欧盟国家的销售额：{exportToEUAmount}");

                // 4. 出口到非欧盟国家的销售额
                var exportToNonEUData = monthSalesData.Where(c =>
                    c.TAX_COLLECTION_RESPONSIBILITY.ToLower() == "seller" &&
                    c.SALE_DEPART_COUNTRY == _countryCode &&
                    (!euCountryList.Contains(c.SALE_ARRIVAL_COUNTRY) &&
                    c.SALE_ARRIVAL_COUNTRY != _countryCode)).ToList();

                var exportToNonEUAmount = CalculateExportAmount(exportToNonEUData, exchangeRateList, month);
                _logger.LogError($"当前计算月份：{month}；出口到非欧盟国家的销售额：{exportToNonEUAmount}");
                totalExportToNonEUAmount += exportToNonEUAmount;
            }
            // 计算box1的值
            

            var euStatistics = new EUAmazonSaleAmountStatistics()
            {
                // 未被代扣销售额
                AmazonSaleAmount = new Money(totalSellerAmount, _currencyCode),
                SellerTaxAmount = new Money(totalComminglingBuyAmount, _currencyCode),
                ExportToEUAmount = new Money(totalExportToEUAmount, _currencyCode),
                ExportToNonEUAmount = new Money(totalExportToNonEUAmount, _currencyCode),
                // 被代扣代缴净销售额
                ExportAmount = new Money(totalMarketplaceTaxAmount, _currencyCode),
                EUAmazonItems = new List<EUAmazonItem>(),
                ZMDeclaredAmount = new Money(0, _currencyCode)
            };
            result.EUAmazonSaleAmountStatistics = euStatistics;
            _logger.LogError($"计算结果：未被代扣销售额{totalSellerAmount}，COMMINGLING_BUY的税金{totalComminglingBuyAmount}，被代扣代缴净销售额{totalMarketplaceTaxAmount}，出口到欧盟国家的销售额：{totalExportToEUAmount}，出口到非欧盟国家的销售额：{totalExportToNonEUAmount}");
            // 构建返回结果
            // result.GBAmazonSaleAmountStatistics = new GBAmazonSaleAmountStatistics
            // {
            //     NormalSaleAmount = new Money(totalSellerTaxAmount, _currencyCode),
            //     NormalSaleAmountAmtTax = new Money(Math.Round(totalSellerTaxAmount * _taxrate, 2), _currencyCode),
            //     MarketplaceSaleAmount = new Money(totalMarketplaceTaxAmount, _currencyCode),
            //     ExportToEUAmount = new Money(totalExportToEUAmount, _currencyCode),
            //     ExportToNonEUAmount = new Money(totalExportToNonEUAmount, _currencyCode)
            // };

            return result;
        }

        /// <summary>
        /// 计算未代扣代缴订单的含税销售额
        /// </summary>
        private decimal CalculateSellerTaxableAmount(List<CustomerSalesData> sellerData, List<VatExchangeRate> exchangeRateList, string month)
        {
            decimal totalAmount = 0;

            // 1. 去掉所有筛选，勾选CQ列为"SELLER"的数据
            var cqSellerData = sellerData.Where(c => c.TAX_COLLECTION_RESPONSIBILITY.ToLower() == "seller").ToList();

            // 2. F列勾选全部之后删除"COMMINGLING_BUY"的数据
            var filteredData = cqSellerData.Where(c => c.TRANSACTION_TYPE != "COMMINGLING_BUY").ToList();

            // 3. BP及BQ列均勾选为"GB"的数据
            var gbData = filteredData.Where(c =>
                c.SALE_DEPART_COUNTRY == _countryCode &&
                c.SALE_ARRIVAL_COUNTRY == _countryCode).ToList();

            // 4. CB列删除"JE, GY, GG, GX"的数据
            var validPostCodeData = gbData.Where(c =>
                c.VAT_CALCULATION_IMPUTATION_COUNTRY.IsNullOrBlank() ||
                (!c.VAT_CALCULATION_IMPUTATION_COUNTRY.StartsWith("JE") &&
                 !c.VAT_CALCULATION_IMPUTATION_COUNTRY.StartsWith("GY") &&
                 !c.VAT_CALCULATION_IMPUTATION_COUNTRY.StartsWith("GG") &&
                 !c.VAT_CALCULATION_IMPUTATION_COUNTRY.StartsWith("GX"))).ToList();
            _logger.LogError($"当前计算月份：{month}；当前月份未代扣代缴订单数据：{string.Join(',', validPostCodeData.Select(item => item.TOTAL_ACTIVITY_VALUE_AMT_VAT_INCL))}；");
            // 5. 求和BA列销售额，将此步求和后的数据做好登记
            // 需要先确认BB列是否有不同币种，如有需要按汇率换算成GBP之后再合并求和
            decimal b2cAmount = 0;
            var currencyGroups = validPostCodeData.Where(c =>
                !c.TOTAL_ACTIVITY_VALUE_AMT_VAT_INCL.IsNullOrBlank())
                .GroupBy(c => c.TRANSACTION_CURRENCY_CODE);

            foreach (var group in currencyGroups)
            {
                var currencyCode = group.Key;
                var currencyAmount = group.Sum(c => decimal.Parse(c.TOTAL_ACTIVITY_VALUE_AMT_VAT_INCL));

                // 获取汇率，转换为GBP
                decimal exchangeRate = 1M;
                if (currencyCode != _currencyCode) // _currencyCode应该是"GBP"
                {
                    var currencyDic = ConstantHelper.GetConstantInfoList(typeof(CurrencyCode)).ToDictionary(c => c.Value, c => c.Description);
                    if (currencyDic.ContainsKey(currencyCode))
                    {
                        exchangeRate = exchangeRateList.Where(c =>
                            c.VatCountryCurrency == "英镑" &&
                            c.ExchangeCurrency == currencyDic[currencyCode] &&
                            c.Month.Equals(month)).Select(c => c.ExchangeRate).FirstOrDefault();

                        if (exchangeRate == 0) exchangeRate = 1M;
                    }
                }

                // 转换为GBP后累加
                b2cAmount += currencyAmount / exchangeRate;
            }
            _logger.LogError($"当前计算月份：{month}；当前月份未代扣代缴销售额：{b2cAmount}；");

            // 6. 未被代扣代缴净销售额=第5步登记好的销售额/(1+税率)
            // var netAmount = b2cAmount / (1 + _taxrate);

            // 7. 未代扣代缴缴纳税金=第6步计算出来的未被代扣代缴净销售额*税率
            // totalAmount = netAmount * _taxrate;

            return b2cAmount;  // Math.Round(totalAmount, 2);
        }

        /// <summary>
        /// 计算"COMMINGLING_BUY"的税金
        /// </summary>
        private decimal CalculateComminglingBuyTax(List<CustomerSalesData> sellerData, List<VatExchangeRate> exchangeRateList, string month)
        {
            // 1. 去掉所有筛选，勾选CQ列为"SELLER"的数据
            var cqSellerData = sellerData.Where(c => c.TAX_COLLECTION_RESPONSIBILITY.ToLower() == "seller").ToList();

            // 2. F列只勾选"COMMINGLING_BUY"的数据
            var comminglingBuyData = cqSellerData.Where(c => c.TRANSACTION_TYPE == "COMMINGLING_BUY").ToList();

            // 3. BP及BQ列均勾选为"GB"的数据
            var gbData = comminglingBuyData.Where(c =>
                c.SALE_DEPART_COUNTRY == _countryCode &&
                c.SALE_ARRIVAL_COUNTRY == _countryCode).ToList();

            // 4. CA列删除空白订单
            var validOrderData = gbData.Where(c => !c.BUYER_VAT_NUMBER.IsNullOrBlank()).ToList();

            // 5. AQ列勾选不等于0的数据
            var nonZeroData = validOrderData.Where(c =>
                !c.TOTAL_ACTIVITY_VALUE_VAT_AMT.IsNullOrBlank() &&
                decimal.Parse(c.TOTAL_ACTIVITY_VALUE_VAT_AMT) != 0).ToList();
            // _logger.LogError($"当前计算月份：{month}；当前月份未代扣代缴COMMINGLING_BUY订单数据：{string.Join(',', nonZeroData.Select(item => item.TOTAL_ACTIVITY_VALUE_AMT_VAT_INCL))}；");

            // 6. 求和AQ列税金，将此步求和后的数据做好登记
            // 求和前请先确认BB列是否有不同币种，如有需要按汇率换算成GBP之后再合并求和
            decimal totalTax = 0;
            var currencyGroups = nonZeroData.GroupBy(c => c.TRANSACTION_CURRENCY_CODE);

            foreach (var group in currencyGroups)
            {
                var currencyCode = group.Key;
                var currencyTaxAmount = group.Sum(c => decimal.Parse(c.TOTAL_ACTIVITY_VALUE_VAT_AMT));

                // 获取汇率，转换为GBP
                decimal exchangeRate = 1M;
                if (currencyCode != _currencyCode) // _currencyCode应该是"GBP"
                {
                    var currencyDic = ConstantHelper.GetConstantInfoList(typeof(CurrencyCode)).ToDictionary(c => c.Value, c => c.Description);
                    if (currencyDic.ContainsKey(currencyCode))
                    {
                        exchangeRate = exchangeRateList.Where(c =>
                            c.VatCountryCurrency == "英镑" &&
                            c.ExchangeCurrency == currencyDic[currencyCode] &&
                            c.Month.Equals(month)).Select(c => c.ExchangeRate).FirstOrDefault();

                        if (exchangeRate == 0) exchangeRate = 1M;
                    }
                }

                // 转换为GBP后累加
                totalTax += currencyTaxAmount / exchangeRate;
            }

            return Math.Round(totalTax, 2);
        }

        /// <summary>
        /// 计算被代扣代缴净销售额
        /// </summary>
        private decimal CalculateMarketplaceTaxAmount(List<CustomerSalesData> monthSalesData, List<VatExchangeRate> exchangeRateList, string month)
        {
            // 第 1 步：去掉所有筛选，筛选对应的申报时间，勾选 CQ 列为“MARKETPLACE”的数据；
            // 第 2 步：BQ 列勾选为“GB”的数据 ；
            // 第 3 步：BP 列全选；
            var marketplaceData = monthSalesData.Where(c =>
                    c.TAX_COLLECTION_RESPONSIBILITY.ToLower() == "marketplace" &&
                    // c.SALE_DEPART_COUNTRY == _countryCode &&
                    c.SALE_ARRIVAL_COUNTRY == _countryCode).ToList();
            // 第4步：求和BA列销售额
            // 求和前请先确认BB列是否有不同币种，如有需要按汇率换算成GBP之后再合并求和
            decimal step4Amount = 0;
            var step4CurrencyGroups = marketplaceData.Where(c =>
                // c.BUYER_VAT_NUMBER.IsNullOrBlank() &&
                !c.TOTAL_ACTIVITY_VALUE_AMT_VAT_INCL.IsNullOrBlank())
                .GroupBy(c => c.TRANSACTION_CURRENCY_CODE);
            // _logger.LogError($"当前计算月份：{month}；当前月份代扣代缴step4订单数据：{string.Join(',', marketplaceData.Select(item => item.TOTAL_ACTIVITY_VALUE_AMT_VAT_INCL))}；");

            foreach (var group in step4CurrencyGroups)
            {
                var currencyCode = group.Key;
                var currencyAmount = group.Sum(c => decimal.Parse(c.TOTAL_ACTIVITY_VALUE_AMT_VAT_INCL));

                // 获取汇率，转换为GBP
                decimal exchangeRate = 1M;
                if (currencyCode != _currencyCode)
                {
                    var currencyDic = ConstantHelper.GetConstantInfoList(typeof(CurrencyCode)).ToDictionary(c => c.Value, c => c.Description);
                    if (currencyDic.ContainsKey(currencyCode))
                    {
                        exchangeRate = exchangeRateList.Where(c =>
                            c.VatCountryCurrency == "英镑" &&
                            c.ExchangeCurrency == currencyDic[currencyCode] &&
                            c.Month.Equals(month)).Select(c => c.ExchangeRate).FirstOrDefault();

                        if (exchangeRate == 0) exchangeRate = 1M;
                    }
                }

                // 转换为GBP后累加
                step4Amount += currencyAmount / exchangeRate;
            }

            // 第5步：去掉所有筛选，筛选对应的申报时间，勾选CQ列为"MARKETPLACE"的数据，然后BP列勾选为"GB"的数据
            var step5Data = monthSalesData.Where(c =>
                c.TAX_COLLECTION_RESPONSIBILITY.ToLower() == "marketplace" &&
                c.SALE_DEPART_COUNTRY == _countryCode).ToList();

            // 第6步：BQ列删除"GB"
            var step6Data = step5Data.Where(c => c.SALE_ARRIVAL_COUNTRY != _countryCode).ToList();

            // 第7步：求和BA列销售额
            // 求和前请先确认BB列是否有不同币种，如有需要按汇率换算成GBP之后再合并求和
            decimal step7Amount = 0;
            var step7CurrencyGroups = step6Data.Where(c =>
                // c.BUYER_VAT_NUMBER.IsNullOrBlank() &&
                !c.TOTAL_ACTIVITY_VALUE_AMT_VAT_INCL.IsNullOrBlank())
                .GroupBy(c => c.TRANSACTION_CURRENCY_CODE);
            // _logger.LogError($"当前计算月份：{month}；当前月份代扣代缴step7订单数据：{string.Join(',', step6Data.Select(item => item.TOTAL_ACTIVITY_VALUE_AMT_VAT_INCL))}；");

            foreach (var group in step7CurrencyGroups)
            {
                var currencyCode = group.Key;
                var currencyAmount = group.Sum(c => decimal.Parse(c.TOTAL_ACTIVITY_VALUE_AMT_VAT_INCL));

                // 获取汇率，转换为GBP
                decimal exchangeRate = 1M;
                if (currencyCode != _currencyCode)
                {
                    var currencyDic = ConstantHelper.GetConstantInfoList(typeof(CurrencyCode)).ToDictionary(c => c.Value, c => c.Description);
                    if (currencyDic.ContainsKey(currencyCode))
                    {
                        exchangeRate = exchangeRateList.Where(c =>
                            c.VatCountryCurrency == "英镑" &&
                            c.ExchangeCurrency == currencyDic[currencyCode] &&
                            c.Month.Equals(month)).Select(c => c.ExchangeRate).FirstOrDefault();

                        if (exchangeRate == 0) exchangeRate = 1M;
                    }
                }

                // 转换为GBP后累加
                step7Amount += currencyAmount / exchangeRate;
            }

            // 第8步：被代扣代缴净销售额=第4步+第7步
            var totalMarketplaceAmount = step4Amount + step7Amount;
            _logger.LogError($"当前计算月份：{month}；当前月份代扣代缴销售额1：{step4Amount}；当前月份代扣代缴销售额2：{step7Amount}；");
            return Math.Round(totalMarketplaceAmount, 2);
        }

        /// <summary>
        /// 计算出口销售额
        /// </summary>
        private decimal CalculateExportAmount(List<CustomerSalesData> exportData, List<VatExchangeRate> exchangeRateList, string month)
        {
            decimal totalAmount = 0;

            // 按币种分组计算
            var currencyGroups = exportData.GroupBy(c => c.TRANSACTION_CURRENCY_CODE);

            foreach (var group in currencyGroups)
            {
                var currencyCode = group.Key;
                var currencyData = group.ToList();

                // 求和BA列销售额，需要换成申报国币种
                var currencyAmount = currencyData.Where(c => !c.TOTAL_ACTIVITY_VALUE_AMT_VAT_INCL.IsNullOrBlank())
                    .Sum(c => decimal.Parse(c.TOTAL_ACTIVITY_VALUE_AMT_VAT_INCL));

                // 获取汇率
                decimal exchangeRate = 1M;
                _logger.LogError($"当前国家代码：{currencyCode}；");

                if (currencyCode != _currencyCode)
                {
                    var currencyDic = ConstantHelper.GetConstantInfoList(typeof(CurrencyCode)).ToDictionary(c => c.Value, c => c.Description);
                    if (currencyDic.ContainsKey(currencyCode))
                    {
                        exchangeRate = exchangeRateList.Where(c =>
                            c.VatCountryCurrency == "英镑" &&
                            c.ExchangeCurrency == currencyDic[currencyCode] &&
                            c.Month.Equals(month)).Select(c => c.ExchangeRate).FirstOrDefault();

                        if (exchangeRate == 0) exchangeRate = 1M;
                    }
                }

                // 转换为申报国币种
                totalAmount += currencyAmount / exchangeRate;
            }

            return Math.Round(totalAmount, 2);
        }
    }
}

using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using BFE.Framework.Domain.Core.Specification;
using BFE.Framework.Infrastructure.Crosscutting.Constant;
using BFE.Framework.Infrastructure.Crosscutting.Helpers;
using CsvHelper;
using Magicodes.ExporterAndImporter.Excel;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Vat.Service.Common.Helpers;
using Vat.Service.Common.Http;
using Vat.Service.Common.Model;
using Vat.Service.Common.Services.Impl.DeclaredCalculateFormula;
using Vat.Service.Common.Util.Txt;
using Vat.Service.Common.Util.Excel;
using Vat.Service.Domain.AggregatesModel.TaxAgg;
using Vat.Service.Domain.AggregatesModel.VATExchangeRateAgg;
using Vat.Service.Domain.AggregatesModel.VATExchangeRateAgg.Specifications;
using Vat.Service.Domain.AggregatesModel.VATExchangeRateAgg.SpecificationsForVATExchangeRateByDay;
using Vat.Service.Domain.Constants;
using Vat.Service.Domain.Repositories.Tax;
using Vat.Service.Domain.Repositories.VatExchangeRate;
using Vat.Service.Infastructure.Exception;
using BFE.Framework.Infrastructure.Crosscutting.Adapter;
using Spire.Pdf;
using Spire.Pdf.Utilities;
using Vat.Service.Common.Model.SaleAmountStatistics;
using Aspose.Pdf.Text;
using Spire.Pdf.Fields;
using Spire.Pdf.Texts;

namespace Vat.Service.Common.Services.Impl
{
    public class DeclaredCalculateService : IDeclaredCalculateService
    {
        private readonly ILogger<DeclaredCalculateService> _logger;
        private readonly FileClient _adminFileClient;
        private readonly IVatExchangeRateRepository _vatExchangeRepository;
        private readonly ITaxNumberRepository _taxNumberRepository;
        private readonly IServiceProvider _serviceProvider;
        private readonly IVatExchangeRateByDayRepository _vatExchangeRateByDayRepository;
        private readonly ITypeAdapter _typeAdapter;

        public DeclaredCalculateService(ILogger<DeclaredCalculateService> logger, FileClient adminFileClient,
            IVatExchangeRateRepository vatExchangeRepository, ITaxNumberRepository taxNumberRepository, IServiceProvider serviceProvider,
            IVatExchangeRateByDayRepository vatExchangeRateByDayRepository, ITypeAdapter typeAdapter)
        {
            _logger = logger;
            _adminFileClient = adminFileClient;
            _vatExchangeRepository = vatExchangeRepository;
            _taxNumberRepository = taxNumberRepository;
            _serviceProvider = serviceProvider;
            _vatExchangeRateByDayRepository = vatExchangeRateByDayRepository;
            _typeAdapter = typeAdapter;
        }

        public async Task<SaleAmountStatisticsResult> AmazonSalesDataCalculate(DateTime startDate, DateTime endDate, string salesFilePath,
            List<string> existVatCountryCodeList, string rateType, TaxNumber taxNumber)
        {
            var result = new SaleAmountStatisticsResult();

            string vatCountryCode = taxNumber.TaxCountry.Code;
            string registerMain = taxNumber.CompanyInfo.Id;
            string tenantCode = taxNumber.Tenant.Code;
            if (vatCountryCode == "SA" || vatCountryCode == "AE")
            {
                result = await ArabAmazonSalesDataCalculate(startDate, endDate, salesFilePath, taxNumber);
                return result;
            }
            else if (vatCountryCode == "MX")
            {
                result = await MxAmazonSalesDataCalculate(startDate, endDate, salesFilePath, taxNumber);
                return result;
            }
            else if (vatCountryCode == "CA")
            {
                result = await CaAmazonSalesDataCalculate(startDate, endDate, salesFilePath, taxNumber);
                return result;
            }
            bool isGBFrs = vatCountryCode == "GB" && rateType == TaxRateType.FRS;
            string DomesticCurrency = "英镑";
            string DestinationCurrency = "欧元";
            if (vatCountryCode != "GB")
            {
                DomesticCurrency = "欧元";
                DestinationCurrency = "英镑";
            }
            if (vatCountryCode == "CZ")
            {
                DomesticCurrency = "捷克克朗";
            }
            if (startDate > endDate)
            {
                //throw new VatCommonException(Error.Codes.AmazonVatSaleamountCalculateError,
                //string.Format(Error.Names.AmazonVatSaleamountCalculateError, "申报周期起始时间不能大于截止时间"));
                throw new Exception("申报周期起始时间不能大于截止时间");
            }

            //获取欧盟所有国家代码
            var euCountryList = ConstantHelper.GetConstantInfoList(typeof(EUCountryInfo)).Select(item => item.Value).ToList();
            var euCountryDic = ConstantHelper.GetConstantInfoList(typeof(EUCountryInfo)).ToDictionary(c => c.Value, c => c.Description);
            var customerSalesDatas = await GetCustomerSalesDatas(salesFilePath, isGBFrs);

            var arrivalCountryList = new List<string>();
            arrivalCountryList.AddRange(euCountryList);
            //获取arrivalCountryList
            if (existVatCountryCodeList != null && existVatCountryCodeList.Count > 0)
            {
                foreach (var item in existVatCountryCodeList)
                {
                    if (arrivalCountryList.Contains(item))
                    {
                        arrivalCountryList.Remove(item);
                    }
                }
            }
            //获取申报月份
            var declareMonthDic = GetDeclareMonth(startDate, endDate);
            var declareMonthList = declareMonthDic.Keys.ToList();

            var exchangeRateMonthList = declareMonthDic.Values.ToList();
            //获取汇率
            ISpecification<VatExchangeRate> specification
                = new MatchVatExchangeRateByIsDeletedSpecification(false);
            specification = specification.And(new MatchVatExchangeRateByMonthStrSpecification(exchangeRateMonthList));
            specification = specification.And(new MatchVatExchangeRateByVatCountryCodeSpecification(vatCountryCode));
            specification = specification.And(new MatchVatExchangeRateByVatCountryCurrencySpecification(DomesticCurrency));
            //specification = specification.And(new MatchVatExchangeRateByExchangeCurrencySpecification(DestinationCurrency));

            var exchangeQuery = await _vatExchangeRepository.GetListAsync(specification);
            var exchangeRateList = exchangeQuery.ToList();
            //币种汇率校验
            if (vatCountryCode == "DE" || vatCountryCode == "FR" || vatCountryCode == "IT" || vatCountryCode == "ES" || vatCountryCode == "CZ" || vatCountryCode == "AT" || vatCountryCode == "SE" || vatCountryCode == "NL")
            {
                var currencyList = ConstantHelper.GetConstantInfoList(typeof(CurrencyCode)).Select(c => c.Description).ToList();
                currencyList.Remove(DomesticCurrency);
                string msg = "";
                foreach (var month in exchangeRateMonthList)
                {
                    var unexistsCurrency = currencyList.Except(exchangeRateList.Where(r => r.Month == month).Select(r => r.ExchangeCurrency));
                    if (unexistsCurrency.Any())
                    {
                        var currencycodes = string.Join("、", unexistsCurrency);
                        msg = string.Format("找不到该月：{0},对应的{1}汇率信息，请联系管理员！", month, currencycodes);
                    }
                }
                if (!msg.IsNullOrBlank())
                {
                    //throw new VatCommonException(Error.Codes.AmazonVatSaleamountCalculateError,
                    //string.Format(Error.Names.AmazonVatSaleamountCalculateError, msg));
                    throw new Exception(msg);
                }
            }
            //else if (vatCountryCode == "AT")
            //{
            //    exchangeRateList = exchangeRateList.Where(c => c.ExchangeCurrency == DomesticCurrency).ToList();
            //    var noExchangeRates = exchangeRateMonthList.Except(exchangeRateList.Select(e => e.Month).ToList()).ToList();
            //    if (noExchangeRates.Any())
            //    {
            //        //noExchangeRates = noExchangeRates.TrimEnd('、');
            //        var months = string.Join("、", noExchangeRates);
            //        var msg = string.Format("找不到该月：{0},对应的汇率信息，请联系管理员！", months);
            //        //throw new VatCommonException(Error.Codes.AmazonVatSaleamountCalculateError,
            //        //string.Format(Error.Names.AmazonVatSaleamountCalculateError, msg));
            //        throw new Exception(msg);
            //    }
            //}
            var newrateType = rateType;
            if (rateType == TaxRateType.Ordinary)
                newrateType = "";
            //amazon销售数据文件计算
            if (vatCountryCode == "PL")
            {
                var dayExchangeRateList = await _vatExchangeRateByDayRepository.GetListAsync(new MatchVatExchangeRateByDayByIsDeletedSpecification(false).And(new MatchVatExchangeRateByDayByVatCountryCodeSpecification(vatCountryCode)));
                PLDeclaredCalculateFormula plDeclaredCalculate = new PLDeclaredCalculateFormula();
                result = await plDeclaredCalculate.AmazonSalesAmountCalculate(customerSalesDatas, declareMonthList, euCountryList, declareMonthDic, arrivalCountryList, dayExchangeRateList.ToList(), euCountryDic, existVatCountryCodeList);
            }
            else
            {
                if (taxNumber.IsTester == true && vatCountryCode == "GB")
                {
                    var type = typeof(IDeclaredCalculateFormula).Assembly.GetTypes().Where(t => t.Name.ToLower() == $"{vatCountryCode}{newrateType}DeclaredCalculateFormulaTest".ToLower()).FirstOrDefault();
                    var types = typeof(IDeclaredCalculateFormula).Assembly.GetTypes().Select(item => item.Name);
                    _logger.LogError($"当前类：{vatCountryCode}{newrateType}DeclaredCalculateFormulaTest");
                    if (type == null)
                    {
                        // throw new VatCommonException(Error.Codes.AmazonVatSaleamountCalculateError,
                        //string.Format(Error.Names.AmazonVatSaleamountCalculateError, "暂时不支持该国家的自动计算"));
                        throw new Exception("暂时不支持该国家的自动计算");
                    }
                    var declaredCalculateFormula = ActivatorUtilities.GetServiceOrCreateInstance(_serviceProvider, type) as IDeclaredCalculateFormula;
                    if (rateType != TaxRateType.Ordinary)
                    {
                        declaredCalculateFormula.SetRateTime(startDate, endDate, taxNumber.TaxStartTime.Value);
                    }

                    result = await declaredCalculateFormula.AmazonSalesAmountCalculate(customerSalesDatas, declareMonthList, euCountryList, declareMonthDic, arrivalCountryList, exchangeRateList, euCountryDic, existVatCountryCodeList);
                }
                else
                {
                    var type = typeof(IDeclaredCalculateFormula).Assembly.GetTypes().Where(t => t.Name.ToLower() == $"{vatCountryCode}{newrateType}DeclaredCalculateFormula".ToLower()).FirstOrDefault();
                    var types = typeof(IDeclaredCalculateFormula).Assembly.GetTypes().Select(item => item.Name);
                    _logger.LogError($"当前类：{vatCountryCode}{newrateType}DeclaredCalculateFormula");
                    if (type == null)
                    {
                        // throw new VatCommonException(Error.Codes.AmazonVatSaleamountCalculateError,
                        //string.Format(Error.Names.AmazonVatSaleamountCalculateError, "暂时不支持该国家的自动计算"));
                        throw new Exception("暂时不支持该国家的自动计算");
                    }
                    var declaredCalculateFormula = ActivatorUtilities.GetServiceOrCreateInstance(_serviceProvider, type) as IDeclaredCalculateFormula;
                    if (rateType != TaxRateType.Ordinary)
                    {
                        declaredCalculateFormula.SetRateTime(startDate, endDate, taxNumber.TaxStartTime.Value);
                    }

                    result = await declaredCalculateFormula.AmazonSalesAmountCalculate(customerSalesDatas, declareMonthList, euCountryList, declareMonthDic, arrivalCountryList, exchangeRateList, euCountryDic, existVatCountryCodeList);
                }

            }
            result.VatCountryCode = vatCountryCode;
            result.RateType = rateType;
            result.ExistVatCountryCodeList.AddRange(existVatCountryCodeList);
            return result;
        }
        /// <summary>
        /// 阿拉伯国家Amazon 计算
        /// </summary>
        /// <param name="startDate"></param>
        /// <param name="endDate"></param>
        /// <param name="salesFilePath"></param>
        /// <param name="taxNumber"></param>
        /// <returns></returns>
        public async Task<SaleAmountStatisticsResult> ArabAmazonSalesDataCalculate(DateTime startDate, DateTime endDate, string salesFilePath, TaxNumber taxNumber)
        {
            string vatCountryCode = taxNumber.TaxCountry.Code;
            var declareMonthDic = GetDeclareMonth(startDate, endDate);
            var declareMonthList = declareMonthDic.Keys.ToList();
            var type = typeof(IDeclaredCalculateFormula).Assembly.GetTypes().Where(t => t.Name.ToLower() == $"{vatCountryCode}DeclaredCalculateFormula".ToLower()).FirstOrDefault();
            var declaredCalculateFormula = ActivatorUtilities.GetServiceOrCreateInstance(_serviceProvider, type) as IDeclaredCalculateFormula;
            var result = new SaleAmountStatisticsResult();
            var arabcustomerSalesDatas = await GetArabCustomerSalesDatas(salesFilePath);
            var customerSalesDatas = _typeAdapter.Adapt<List<CustomerSalesData>>(arabcustomerSalesDatas);
            //转换阿拉伯国家数据结构
            result = await declaredCalculateFormula.AmazonSalesAmountCalculate(customerSalesDatas, declareMonthList, null, declareMonthDic, null, null, null, null);
            result.VatCountryCode = vatCountryCode;
            return result;
        }
        public async Task<SaleAmountStatisticsResult> MxAmazonSalesDataCalculate(DateTime startDate, DateTime endDate, string salesFilePath, TaxNumber taxNumber)
        {
            string vatCountryCode = taxNumber.TaxCountry.Code;
            var declareMonthDic = GetDeclareMonth(startDate, endDate);
            var declareMonthList = declareMonthDic.Keys.ToList();
            var type = typeof(IDeclaredCalculateFormula).Assembly.GetTypes().Where(t => t.Name.ToLower() == $"{vatCountryCode}DeclaredCalculateFormula".ToLower()).FirstOrDefault();
            var declaredCalculateFormula = ActivatorUtilities.GetServiceOrCreateInstance(_serviceProvider, type) as IDeclaredCalculateFormula;
            var result = new SaleAmountStatisticsResult();
            var mxcustomerSalesDatas = await GetMXCustomerSalesDatas(salesFilePath);
            var customerSalesDatas = _typeAdapter.Adapt<List<CustomerSalesData>>(mxcustomerSalesDatas);
            //转换墨西哥数据结构
            result = await declaredCalculateFormula.AmazonSalesAmountCalculate(customerSalesDatas, declareMonthList, null, declareMonthDic, null, null, null, null);
            result.VatCountryCode = vatCountryCode;
            return result;
        }
        public async Task<SaleAmountStatisticsResult> CaAmazonSalesDataCalculate(DateTime startDate, DateTime endDate, string salesFilePath, TaxNumber taxNumber)
        {
            string vatCountryCode = taxNumber.TaxCountry.Code;
            var declareMonthDic = GetDeclareMonth(startDate, endDate);
            var declareMonthList = declareMonthDic.Keys.ToList();
            var type = typeof(IDeclaredCalculateFormula).Assembly.GetTypes().Where(t => t.Name.ToLower() == $"{vatCountryCode}DeclaredCalculateFormula".ToLower()).FirstOrDefault();
            var declaredCalculateFormula = ActivatorUtilities.GetServiceOrCreateInstance(_serviceProvider, type) as IDeclaredCalculateFormula;
            var result = new SaleAmountStatisticsResult();
            var cacustomerSalesDatas = await GetCaCustomerSalesDatas(salesFilePath);
            var customerSalesDatas = _typeAdapter.Adapt<List<CustomerSalesData>>(cacustomerSalesDatas);
            //转换加拿大数据结构
            result = await declaredCalculateFormula.AmazonSalesAmountCalculate(customerSalesDatas, declareMonthList, null, declareMonthDic, null, null, null, null);
            result.VatCountryCode = vatCountryCode;
            return result;
        }
        public async Task<EBaySaleAmountStatistics> EBaySalesDataCalculate(DateTime startDate, DateTime endDate, string salesFilePath, TaxNumber taxNumber)
        {
            //下载文件，解析，校验，转换成数据集合
            //不同国家实现ebay的计算。
            string vatCountryCode = taxNumber.TaxCountry.Code;
            string registerMain = taxNumber.CompanyInfo.Id;
            string tenantCode = taxNumber.Tenant.Code;
            var newrateType = "";
            var declareMonthDic = GetDeclareMonth(startDate, endDate);
            var declareMonthList = declareMonthDic.Keys.ToList();
            var exchangeRateMonthList = declareMonthDic.Values.ToList();
            var DomesticCurrency = "英镑";
            //获取汇率
            ISpecification<VatExchangeRate> specification
                = new MatchVatExchangeRateByIsDeletedSpecification(false);
            specification = specification.And(new MatchVatExchangeRateByMonthStrSpecification(exchangeRateMonthList));
            specification = specification.And(new MatchVatExchangeRateByVatCountryCodeSpecification(vatCountryCode));
            specification = specification.And(new MatchVatExchangeRateByVatCountryCurrencySpecification(DomesticCurrency));

            var exchangeQuery = await _vatExchangeRepository.GetListAsync(specification);
            var exchangeRateList = exchangeQuery.Where(e => e.ExchangeCurrency == "美元").ToList();
            //校验汇率
            var noExchangeRates = exchangeRateMonthList.Except(exchangeRateList.Select(r => r.Month));
            if (noExchangeRates.Any())
            {
                var months = string.Join("、", noExchangeRates);
                var msg = string.Format("找不到该月：{0},对应的美元汇率信息，请联系管理员！", months);
                throw new Exception(msg);
            }
            var ebaySalesDatas = await GeteBaySalesData(salesFilePath);

            var type = typeof(IDeclaredCalculateFormula).Assembly.GetTypes().Where(t => t.Name.ToLower() == $"{vatCountryCode}{newrateType}DeclaredCalculateFormula".ToLower()).FirstOrDefault();
            var declaredCalculateFormula = ActivatorUtilities.GetServiceOrCreateInstance(_serviceProvider, type) as IDeclaredCalculateFormula;
            var result = await declaredCalculateFormula.EBaySalesAmountCalculate(ebaySalesDatas, declareMonthList, exchangeRateList);
            return result;
        }
        public async Task<DayAmazonSaleAmountStatistics> AliexpressSalesDataCalculate(DateTime startDate, DateTime endDate, string salesFilePath, TaxNumber taxNumber, List<string> existVatCountryCodeList)
        {
            DayAmazonSaleAmountStatistics result = new DayAmazonSaleAmountStatistics();
            if (taxNumber.TaxCountry.Code == "PL")
            {
                var dayExchangeRateList = await _vatExchangeRateByDayRepository.GetListAsync(new MatchVatExchangeRateByDayByIsDeletedSpecification(false).And(new MatchVatExchangeRateByDayByVatCountryCodeSpecification("PL")));
                //校验汇率
                List<string> datelist = GetDateRangeStr(startDate.AddDays(-1), endDate.AddDays(-1));
                List<string> currencyCodes = new List<string> { "人民币", "美元" };
                List<string> errors = new List<string>();
                foreach (var item in currencyCodes)
                {
                    List<string> rateDateList = dayExchangeRateList.Where(e => e.ExchangeCurrency == item && e.VatCountry.Code == taxNumber.TaxCountry.Code).Select(e => e.Date).ToList();
                    var ignoreDateList = datelist.Except(rateDateList);
                    if (ignoreDateList.Count() > 0)
                    {
                        errors.Add($"币种：{item}，日期：{string.Join(",", ignoreDateList)}；");
                    }
                }
                if (errors.Count() > 0)
                {
                    string msg = "找不到对应的汇率：" + string.Join(" ", errors);
                    throw new Exception(msg);
                }

                var customData = await GetAliexpressSalesData(salesFilePath);
                //获取申报月份
                var declareMonthDic = GetDeclareMonth(startDate, endDate);
                var declareMonthList = declareMonthDic.Keys.ToList();
                var euCountryList = ConstantHelper.GetConstantInfoList(typeof(EUCountryInfo)).Select(item => item.Value).ToList();

                PLDeclaredCalculateFormula plDeclaredCalculate = new PLDeclaredCalculateFormula();
                result = await plDeclaredCalculate.AliexpressSalesAmountCalculate(customData, declareMonthList, euCountryList, dayExchangeRateList.ToList(), existVatCountryCodeList);
            }
            return result;
        }
        public async Task<List<AliexpressSalesData>> GetAliexpressSalesData2(string aliexpressFilePath)
        {
            List<AliexpressSalesData> data = new List<AliexpressSalesData>();
            string fileExt = Path.GetExtension(aliexpressFilePath);
            if (fileExt.ToLower() != ".xlsx" && fileExt.ToLower() != ".zip" && fileExt.ToLower() != ".rar")
            {
                //throw new VatCommonException(Error.Codes.AmazonVatSaleamountCalculateError,
                //string.Format(Error.Names.AmazonVatSaleamountCalculateError, "非Aliexpress格式的Excle或zip文件"));
                throw new Exception("非Aliexpress格式的Excle或zip文件");
            }
            //通过接口获取文件
            var fileResult = await _adminFileClient.GetFile(aliexpressFilePath);

            string saveName = Guid.NewGuid().ToString("N") + fileExt;
            var path = Directory.GetCurrentDirectory();
            string sourcePath = Path.Combine(path, "StaticFiles");
            if (!Directory.Exists(sourcePath))
            {
                Directory.CreateDirectory(sourcePath);
            }
            var sourcefilePath = Path.Combine(sourcePath, saveName);
            //保存文件
            using (var fileStream = new FileStream(sourcefilePath, FileMode.Create))
            {
                await fileResult.CopyToAsync(fileStream);
            }

            var filePath = sourcefilePath;
            string txtFolder = Guid.NewGuid().ToString("N");
            string desPath = string.Empty;
            if (fileExt.ToLower().Equals(".zip") || fileExt.ToLower().Equals(".rar"))
            {
                desPath = Path.Combine(sourcePath, txtFolder);
                if (!Directory.Exists(desPath))
                {
                    Directory.CreateDirectory(desPath);
                }
                try
                {
                    System.IO.Compression.ZipFile.ExtractToDirectory(sourcefilePath, desPath);
                }
                catch (Exception ex)
                {
                    //throw new VatCommonException(Error.Codes.AmazonVatSaleamountCalculateError,
                    //    string.Format("zip压缩包解压失败,异常信息：{0}", ex.Message));
                    throw new Exception(string.Format("zip压缩包解压失败,异常信息：{0}", ex.Message));
                }

                DirectoryInfo root = new DirectoryInfo(desPath);
                System.IO.FileInfo[] files = root.GetFiles();
                if (files.Count().Equals(0))
                {
                    //throw new VatCommonException(Error.Codes.AmazonVatSaleamountCalculateError, "zip压缩包为空");
                    throw new Exception("zip压缩包为空");
                }
                else if (files.Count() > 1)
                {
                    //throw new VatCommonException(Error.Codes.AmazonVatSaleamountCalculateError, "zip压缩包内文件数量大于1");
                    throw new Exception("zip压缩包内文件数量大于1");
                }
                filePath = files[0].FullName;
            }
            try
            {
                IExcelImporter Importer = new ExcelImporter();
                var import = await Importer.Import<AliexpressSalesData>(sourcefilePath);
                if (import.HasError && import.TemplateErrors.Count > 0)
                {
                    //throw new VatCommonException(Error.Codes.AmazonVatSaleamountCalculateError,
                    //  string.Format(Error.Names.AmazonVatSaleamountCalculateError, $"销售数据内容有误,{string.Join(",", import.TemplateErrors.Select(e => e.Message))}"));
                    throw new Exception($"销售数据内容有误,{string.Join(",", import.TemplateErrors.Select(e => e.Message))}");
                }
                data = import.Data.ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Aliexpress销售数据文件读取异常：{ex.Message}");
                //throw new VatCommonException(Error.Codes.AmazonVatSaleamountCalculateError,
                //   string.Format(Error.Names.AmazonVatSaleamountCalculateError, "销售数据内容有误"));
                throw new Exception("销售数据内容有误");
            }
            //删除临时文件
            System.IO.File.Delete(sourcefilePath);
            if (!string.IsNullOrEmpty(desPath))
            {
                DeleteDir(desPath);
            }
            return data;
        }
        public async Task<List<AliexpressSalesData>> GetAliexpressSalesData(string aliexpressFilePath)
        {
            List<AliexpressSalesData> data = new List<AliexpressSalesData>();
            Dictionary<string, string> filedic = new Dictionary<string, string>();
            string fileExt = Path.GetExtension(aliexpressFilePath);
            List<string> extType = new List<string> { ".xlsx", ".xls", ".zip", ".rar" };
            //if (fileExt.ToLower() != ".xlsx" && fileExt.ToLower() != ".zip" && fileExt.ToLower() != ".rar")
            if (!extType.Contains(fileExt))
            {
                throw new Exception("非Aliexpress格式的Excle文件");
            }
            //通过接口获取文件
            var fileResult = await _adminFileClient.GetFileContent(aliexpressFilePath);
            var oristream = await fileResult.ReadAsStreamAsync();

            string saveName = Guid.NewGuid().ToString("N") + fileExt;
            var path = Directory.GetCurrentDirectory();
            string sourcePath = Path.Combine(path, "StaticFiles");
            if (!Directory.Exists(sourcePath))
            {
                Directory.CreateDirectory(sourcePath);
            }
            var sourcefilePath = Path.Combine(sourcePath, saveName);
            //保存文件
            using (var fileStream = new FileStream(sourcefilePath, FileMode.Create))
            {
                await oristream.CopyToAsync(fileStream);
            }
            List<string> datafiles = new List<string>() { sourcefilePath };
            filedic.Add(sourcefilePath, fileResult.Headers?.ContentDisposition?.FileNameStar ?? sourcefilePath);
            //var filePath = sourcefilePath;
            string txtFolder = Guid.NewGuid().ToString("N");
            string desPath = string.Empty;
            if (fileExt.ToLower().Equals(".zip") || fileExt.ToLower().Equals(".rar"))
            {
                desPath = Path.Combine(sourcePath, txtFolder);
                if (!Directory.Exists(desPath))
                {
                    Directory.CreateDirectory(desPath);
                }
                try
                {
                    FileHelper.ExtractToDirectory(sourcefilePath, desPath);
                }
                catch (Exception ex)
                {
                    RemoveTempFile(sourcefilePath, desPath);
                    throw new Exception(string.Format($"{fileExt.Replace(".", "")}压缩包解压失败,异常信息：{0}", ex.Message));
                }

                DirectoryInfo root = new DirectoryInfo(desPath);

                var fileInfosDic = new Dictionary<string, FileInfo>();

                _adminFileClient.GetFileInfosByDirectoryInfo(fileInfosDic, root);

                if (fileInfosDic.Count().Equals(0))
                {
                    RemoveTempFile(sourcefilePath, desPath);
                    throw new Exception($"{fileExt.Replace(".", "")}压缩包为空");
                }
                //filePath = files[0].FullName;
                datafiles = fileInfosDic.Keys.ToList();
                filedic = fileInfosDic.Values.ToList().ToDictionary(f => f.FullName, y => y.Name);
            }
            List<string> errmsg = new List<string>();
            foreach (var item in datafiles)
            {
                try
                {
                    //IExcelImporter Importer = new ExcelImporter();
                    //var import = await Importer.Import<AliexpressSalesData>(item);
                    #region NPOI
                    var excelHelper = new ExcelHelper<AliexpressSalesData>();
                    var import = await excelHelper.GetExcelData(item);
                    #endregion
                    if (import.TemplateErrors != null && import.TemplateErrors.Count > 0)
                    {
                        errmsg.Add($"{Path.GetFileName(filedic[item])}, {string.Join(",", import.TemplateErrors.Select(e => $"{e.RequireColumnName}:{e.Message}"))}");
                        continue;
                    }
                    data.AddRange(import.Data.ToList());
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, $"Aliexpress销售数据文件读取异常：{ex.Message}");
                    errmsg.Add($"文件【{Path.GetFileName(filedic[item])}】, {ex.Message} ;");
                }
            }
            if (errmsg.Any())
            {
                RemoveTempFile(sourcefilePath, desPath);
                throw new Exception($"{string.Join(";", errmsg)}");
            }
            RemoveTempFile(sourcefilePath, desPath);

            //删除临时文件
            //System.IO.File.Delete(sourcefilePath);
            //if (!string.IsNullOrEmpty(desPath))
            //{
            //    DeleteDir(desPath);
            //}
            return data;
        }
        public async Task<AliexpressSaleAmountStatistics> EuAliexpressSalesDataCalculate(DateTime startDate, DateTime endDate, string salesFilePath, TaxNumber taxNumber, List<string> existVatCountryCodeList)
        {
            string vatCountryCode = taxNumber.TaxCountry.Code;
            string currencyCode = "欧元";
            if (vatCountryCode == "GB")
            {
                currencyCode = "英镑";
            }
            else if (vatCountryCode == "CZ")
            {
                currencyCode = "捷克克朗";
            }
            //获取申报月份
            var declareMonthDic = GetDeclareMonth(startDate, endDate);
            var declareMonthList = declareMonthDic.Values.ToList();
            var euCountryList = ConstantHelper.GetConstantInfoList(typeof(EUCountryInfo)).Select(item => item.Value).ToList();
            //获取汇率
            ISpecification<VatExchangeRate> specification
                = new MatchVatExchangeRateByIsDeletedSpecification(false);
            specification = specification.And(new MatchVatExchangeRateByMonthStrSpecification(declareMonthList));
            specification = specification.And(new MatchVatExchangeRateByVatCountryCodeSpecification(vatCountryCode));
            specification = specification.And(new MatchVatExchangeRateByVatCountryCurrencySpecification(currencyCode));
            var exchangeQuery = await _vatExchangeRepository.GetListAsync(specification);
            //校验汇率
            //List<string> datelist = Common.Helper.DateTimeHelper.GetDateRangeStr(startDate.AddDays(-1), endDate.AddDays(-1));
            List<string> currencyCodes = new List<string> { "人民币", "美元" };
            List<string> errors = new List<string>();
            foreach (var item in currencyCodes)
            {
                List<string> rateDateList = exchangeQuery.Where(e => e.ExchangeCurrency == item && e.VatCountry.Code == taxNumber.TaxCountry.Code).Select(e => e.Month).ToList();
                var ignoreDateList = declareMonthList.Except(rateDateList);
                if (ignoreDateList.Count() > 0)
                {
                    errors.Add($"币种：{item}，日期：{string.Join(",", ignoreDateList)}；");
                }
            }
            if (errors.Count() > 0)
            {
                string msg = "找不到对应的汇率，" + string.Join(" ", errors);
                // throw new VatCommonException(Error.Codes.AmazonVatSaleamountCalculateError,
                //string.Format(Error.Names.AmazonVatSaleamountCalculateError, msg));
                throw new Exception(msg);
            }
            var customData = await GetAliexpressSalesData(salesFilePath); ;

            var type = typeof(IDeclaredCalculateFormula).Assembly.GetTypes().Where(t => t.Name.ToLower() == $"{vatCountryCode}DeclaredCalculateFormula".ToLower()).FirstOrDefault();
            var declaredCalculateFormula = ActivatorUtilities.GetServiceOrCreateInstance(_serviceProvider, type) as IDeclaredCalculateFormula;
            AliexpressSaleAmountStatistics result = await declaredCalculateFormula.AliexpressSalesAmountCalculate(customData, declareMonthList, euCountryList, exchangeQuery.ToList(), existVatCountryCodeList); ;
            return result;
        }

        /// <summary>
        /// 应缴税额计算
        /// </summary>
        /// <param name="taxNumber"></param>
        /// <param name="rateType"></param>
        /// <param name="param"></param>
        /// <returns></returns>
        public async Task<Dictionary<string, Money>> VatPayableCalculate(TaxNumber taxNumber, VatPayableCalculateParam param)
        {
            var rate = taxNumber.TaxRate;
            string rateType = param.TaxRateType;
            string vatCountryCode = taxNumber.TaxCountry.Code;
            var newrateType = rateType;
            if (rateType == TaxRateType.Ordinary)
                newrateType = "";
            var model = new VatDeclaredCalcModel()
            {
                TaxRate = rate / 100,
                DeclareItems = param.DeclaredItems,
                AmazonSaleAmount = param.AmazonDeclaredSalesAmount,
                AmazonAmtTax = param.AmazonAmtTax,
                OtherSaleTax = param.OtherSaleTax,
                AmazonDeclaredSalesAmount = param.AmazonDeclaredSalesAmount,
                AmazonDeclaredSalesAmountExclTax = param.AmazonDeclaredSalesAmountExclTax,
                OtherDeclaredSalesAmount = param.OtherDeclaredSalesAmount,
                OtherDeclaredSalesAmountInclTax = param.OtherDeclaredSalesAmountInclTax,
                AmazonDeclaredSalesAmount_OtherRate = param.AmazonDeclaredSalesAmount_OtherRate,
                OtherDeclaredSalesAmount_OtherRate = param.OtherDeclaredSalesAmount_OtherRate,
                AmazonDeclaredSalesAmountAmtTax = param.AmazonDeclaredSalesAmountAmtTax,
                OtherDeclaredSalesAmountLater = param.OtherDeclaredSalesAmountLater,
                EBaySellerCollectedTaxAmount = param.EBaySellerCollectedTaxAmount,
                EBaySoldForAmount = param.EBaySoldForAmount,
                EBayTotalPriceAmount = param.EBayTotalPriceAmount,
                ExportAmount = param.ExportAmount,
                ExportOutSideAmount = param.ExportOutSideAmount,
                OtherExportAmount = param.OtherExportAmount,
                ZMDeclaredAmount = param.ZMDeclaredAmount,
                OtherZMDeclaredAmount = param.OtherZMDeclaredAmount,
                AliexpressExportAmount = param.AliexpressExportAmount,
                AliexpressSalesAmount = param.AliexpressSalesAmount,
                AliexpressZMDeclaredAmount = param.AliexpressZMDeclaredAmount,

                NativeB2BDeclaredAmount = param.NativeB2BDeclaredAmount,
                OtherNativeB2BDeclaredAmount = param.OtherNativeB2BDeclaredAmount,
                AliexpressNativeB2BDeclaredAmount = param.AliexpressNativeB2BDeclaredAmount,
                ExpenseAmount = param.ExpenseAmount,
                TaxPayAmount = param.TaxPayAmount,
                ProductSalesAmount = param.ProductSalesAmount
            };
            Dictionary<string, Money> result = new Dictionary<string, Money>();
            if (vatCountryCode == "PL")
            {
                PLDeclaredCalculateFormula plDeclaredCalculate = new PLDeclaredCalculateFormula();
                result = plDeclaredCalculate.VatPayableCalculate(model);
            }
            else
            {
                var type = typeof(IDeclaredCalculateFormula).Assembly.GetTypes().Where(t => t.Name.ToLower() == $"{vatCountryCode}{newrateType}DeclaredCalculateFormula".ToLower()).FirstOrDefault();
                var declaredCalculateFormula = ActivatorUtilities.GetServiceOrCreateInstance(_serviceProvider, type) as IDeclaredCalculateFormula;
                result = declaredCalculateFormula.VatPayableCalculate(model);
            }
            if (result.Values.Where(p => p.Amount == 0).ToList().Count == result.Count)
            {
                result.Add("ZeroDeclaration", new Money());
            }
            else
            {
                result.Add("NonZeroDeclaration", new Money());
            }
            return result;

        }

        public async Task<DeclaredDataModel> ConvertToDeclaredData(TaxNumber taxNumber, VatPayableCalculateParam param, List<DeductionItemModel> deductionItems)
        {
            var rate = taxNumber.TaxRate;
            string rateType = param.TaxRateType;
            string vatCountryCode = taxNumber.TaxCountry.Code;
            var newrateType = rateType;
            if (rateType == TaxRateType.Ordinary)
                newrateType = "";
            var model = new VatDeclaredCalcModel()
            {
                TaxRate = rate / 100,
                DeclareItems = param.DeclaredItems,
                AmazonDeclaredSalesAmount = param.AmazonDeclaredSalesAmount,
                OtherDeclaredSalesAmount = param.OtherDeclaredSalesAmount,
                AmazonDeclaredSalesAmountExclTax = param.AmazonDeclaredSalesAmountExclTax,
                OtherDeclaredSalesAmountInclTax = param.OtherDeclaredSalesAmountInclTax,
                AmazonDeclaredSalesAmount_OtherRate = param.AmazonDeclaredSalesAmount_OtherRate,
                OtherDeclaredSalesAmount_OtherRate = param.OtherDeclaredSalesAmount_OtherRate,
                OtherDeclaredSalesAmountLater = param.OtherDeclaredSalesAmountLater,
                AmazonDeclaredSalesAmountAmtTax = param.AmazonDeclaredSalesAmountAmtTax,
                EBayTotalPriceAmount = param.EBayTotalPriceAmount,
                EBaySoldForAmount = param.EBaySoldForAmount,
                EBaySellerCollectedTaxAmount = param.EBaySellerCollectedTaxAmount,
                ExportAmount = param.ExportAmount,
                OtherExportAmount = param.OtherExportAmount,
                ZMDeclaredAmount = param.ZMDeclaredAmount,
                AliexpressExportAmount = param.AliexpressExportAmount,
                AliexpressSalesAmount = param.AliexpressSalesAmount,
                AliexpressZMDeclaredAmount = param.AliexpressZMDeclaredAmount,
                OtherZMDeclaredAmount = param.OtherZMDeclaredAmount,
                AmazonSaleAmount = param.AmazonDeclaredSalesAmount,
                AmazonAmtTax = param.AmazonAmtTax,
                OtherSaleTax = param.OtherSaleTax,
                ExpenseAmount = param.ExpenseAmount,
                ExportOutSideAmount = param.ExportOutSideAmount,
                TaxPayAmount = param.TaxPayAmount,
                ProductSalesAmount = param.ProductSalesAmount,
                GBAmazonSaleAmount = param.GBAmazonSaleAmount,
                GBAmazonSaleTaxAmount = param.GBAmazonSaleTaxAmount,
                GBMarketplaceTaxAmount = param.GBMarketplaceTaxAmount,
                GBExportToEUAmount = param.GBExportToEUAmount,
                GBExportToNonEUAmount = param.GBExportToNonEUAmount,
                SellerTaxAmount = param.SellerTaxAmount,
                ExportToEUAmount = param.ExportToEUAmount,
                ExportToNonEUAmount = param.ExportToNonEUAmount
            };
            var dicDeduction = new Dictionary<string, decimal>();
            deductionItems = deductionItems.Where(d => !d.Code.IsNullOrBlank()).ToList();
            foreach (var item in deductionItems)
            {
                dicDeduction.Add(item.Code, item.Amount.Amount);
            }
            model.DeclareItems = dicDeduction;
            DeclaredDataModel result = new DeclaredDataModel();
            if (vatCountryCode == "PL")
            {
                PLDeclaredCalculateFormula plDeclaredCalculate = new PLDeclaredCalculateFormula();
                result = plDeclaredCalculate.ConvertToDeclaredData(model, deductionItems);
            }
            else
            {
                if (param.IsTester == true && vatCountryCode == "GB")
                {
                    var type = typeof(IDeclaredCalculateFormula).Assembly.GetTypes().Where(t => t.Name.ToLower() == $"{vatCountryCode}{newrateType}DeclaredCalculateFormulaTest".ToLower()).FirstOrDefault();
                    var declaredCalculateFormula = ActivatorUtilities.GetServiceOrCreateInstance(_serviceProvider, type) as IDeclaredCalculateFormula;
                    result = declaredCalculateFormula.ConvertToDeclaredData(model, deductionItems);
                }
                else
                {
                    var type = typeof(IDeclaredCalculateFormula).Assembly.GetTypes().Where(t => t.Name.ToLower() == $"{vatCountryCode}{newrateType}DeclaredCalculateFormula".ToLower()).FirstOrDefault();
                    var declaredCalculateFormula = ActivatorUtilities.GetServiceOrCreateInstance(_serviceProvider, type) as IDeclaredCalculateFormula;
                    result = declaredCalculateFormula.ConvertToDeclaredData(model, deductionItems);
                }
            }
            return result;
        }

        /// <summary>
        /// 通过文件地址，读取文件内容
        /// TenantFile/D34103B1CFDD4D5EA9E0866809A83A8B.txt
        /// https://api.globalpcf.net/fileService/Tenant/TenantFile/43DBDD69B2C84A71B45E39AC169F4878.txt
        /// </summary>
        /// <param name="filePath"></param>
        /// <returns></returns>
        private async Task<List<CustomerSalesData>> GetCustomerSalesDatas(string orifilePath, bool isGBFrs)
        {
            List<CustomerSalesData> models = new List<CustomerSalesData>();
            Dictionary<string, string> filedic = new Dictionary<string, string>();
            string fileExt = Path.GetExtension(orifilePath).ToLower();
            List<string> extTypes = new List<string> { ".txt", ".zip", ".rar", ".xls", ".xlsx", ".csv" };
            //if (fileExt.ToLower() != ".txt" && fileExt.ToLower() != ".zip" && fileExt.ToLower() != ".rar")
            if (!extTypes.Contains(fileExt))
            {
                //throw new VatCommonException(Error.Codes.AmazonVatSaleamountCalculateError,
                //string.Format(Error.Names.AmazonVatSaleamountCalculateError, "非amazon格式的txt或zip文件"));
                throw new Exception("非amazon格式的销售数据文件");
            }
            //通过接口获取文件
            var fileResult = await _adminFileClient.GetFileContent(orifilePath);
            var oristream = await fileResult.ReadAsStreamAsync();

            string saveName = Guid.NewGuid().ToString("N") + fileExt;
            var path = Directory.GetCurrentDirectory();
            string sourcePath = Path.Combine(path, "StaticFiles");
            if (!Directory.Exists(sourcePath))
            {
                Directory.CreateDirectory(sourcePath);
            }
            var sourcefilePath = Path.Combine(sourcePath, saveName);
            //保存文件
            using (var fileStream = new FileStream(sourcefilePath, FileMode.Create))
            {
                await oristream.CopyToAsync(fileStream);
            }
            List<string> datafiles = new List<string>() { sourcefilePath };
            filedic.Add(sourcefilePath, fileResult.Headers?.ContentDisposition?.FileNameStar ?? sourcefilePath);
            //var filePath = sourcefilePath;
            string txtFolder = Guid.NewGuid().ToString("N");
            string desPath = string.Empty;
            if (fileExt.ToLower().Equals(".zip") || fileExt.ToLower().Equals(".rar"))
            {
                desPath = Path.Combine(sourcePath, txtFolder);
                if (!Directory.Exists(desPath))
                {
                    Directory.CreateDirectory(desPath);
                }
                try
                {
                    //System.IO.Compression.ZipFile.ExtractToDirectory(sourcefilePath, desPath);
                    FileHelper.ExtractToDirectory(sourcefilePath, desPath);
                }
                catch (Exception ex)
                {
                    //throw new VatCommonException(Error.Codes.AmazonVatSaleamountCalculateError,
                    //    string.Format("zip压缩包解压失败,异常信息：{0}", ex.Message));
                    RemoveTempFile(sourcefilePath, desPath);
                    throw new Exception(string.Format("zip压缩包解压失败,异常信息：{0}", ex.Message));
                }

                DirectoryInfo root = new DirectoryInfo(desPath);
                //System.IO.FileInfo[] files = root.GetFiles();
                var fileInfosDic = new Dictionary<string, FileInfo>();
                _adminFileClient.GetFileInfosByDirectoryInfo(fileInfosDic, root);

                if (fileInfosDic.Count().Equals(0))
                {
                    //throw new VatCommonException(Error.Codes.AmazonVatSaleamountCalculateError, "zip压缩包为空");
                    RemoveTempFile(sourcefilePath, desPath);
                    throw new Exception("zip压缩包为空");
                }
                //else if (files.Count() > 1)
                //{
                //    //throw new VatCommonException(Error.Codes.AmazonVatSaleamountCalculateError, "zip压缩包内文件数量大于1");
                //    RemoveTempFile(sourcefilePath, desPath);
                //    throw new Exception("zip压缩包内文件数量大于1");
                //}
                //filePath = files[0].FullName;
                //fileExt= Path.GetExtension(filePath).ToLower();
                datafiles = fileInfosDic.Keys.ToList();
                filedic = fileInfosDic.Values.ToList().ToDictionary(f => f.FullName, y => y.Name);
            }
            List<string> errmsg = new List<string>();
            foreach (var item in datafiles)
            {
                string filePath = item;
                var cfileExt = Path.GetExtension(filePath).ToLower();
                List<CustomerSalesData> tempdata = new List<CustomerSalesData>();
                try
                {
                    if (cfileExt.Equals(".txt"))
                        tempdata = await ReadAmazonTxtFile(filePath, sourcefilePath, desPath);
                    else if (cfileExt.Equals(".xls") || cfileExt.Equals(".xlsx"))
                        tempdata = await ReadAmazonExcelFile(filePath, sourcefilePath, desPath);
                    else if (cfileExt.Equals(".csv"))
                    {
                        try
                        {
                            Encoding.RegisterProvider(CodePagesEncodingProvider.Instance);
                            using (var reader = new StreamReader(filePath, Encoding.GetEncoding("utf-8")))
                            {
                                var csvReader = new CsvReader(reader, new CsvHelper.Configuration.CsvConfiguration(new System.Globalization.CultureInfo("zh-CHS"))
                                {
                                    BadDataFound = null,
                                    Delimiter = ",",
                                    HasHeaderRecord = true,
                                });
                                csvReader.Context.RegisterClassMap(typeof(CustomerSalesDataMap));
                                tempdata = csvReader.GetRecords<CustomerSalesData>().ToList();
                            }
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(ex, $"计算amazon csv格式数据异常：文件：{orifilePath}");
                            RemoveTempFile(sourcefilePath, desPath);
                            throw new Exception("csv格式数据异常");
                        }
                        RemoveTempFile(sourcefilePath, desPath);
                    }
                    models.AddRange(tempdata);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, $"Amazon销售数据文件读取异常：{ex.Message}");
                    errmsg.Add($"文件【{filedic[item]}】, {ex.Message} ");
                }
            }
            if (errmsg.Any())
            {
                RemoveTempFile(sourcefilePath, desPath);
                throw new Exception($"{string.Join(";", errmsg)}");
            }
            RemoveTempFile(sourcefilePath, desPath);
            return models;
        }
        private async Task<List<MXCustomerSalesData>> GetMXCustomerSalesDatas(string orifilePath)
        {
            List<MXCustomerSalesData> models = new List<MXCustomerSalesData>();
            Dictionary<string, string> filedic = new Dictionary<string, string>();
            string fileExt = Path.GetExtension(orifilePath).ToLower();
            List<string> extTypes = new List<string> { ".zip", ".rar", ".csv" };
            if (!extTypes.Contains(fileExt))
            {
                throw new Exception("非amazon格式的销售数据文件");
            }
            //通过接口获取文件
            var fileResult = await _adminFileClient.GetFileContent(orifilePath);
            var oristream = await fileResult.ReadAsStreamAsync();

            string saveName = Guid.NewGuid().ToString("N") + fileExt;
            var path = Directory.GetCurrentDirectory();
            string sourcePath = Path.Combine(path, "StaticFiles");
            if (!Directory.Exists(sourcePath))
            {
                Directory.CreateDirectory(sourcePath);
            }
            var sourcefilePath = Path.Combine(sourcePath, saveName);
            //保存文件
            using (var fileStream = new FileStream(sourcefilePath, FileMode.Create))
            {
                await oristream.CopyToAsync(fileStream);
            }
            List<string> datafiles = new List<string>() { sourcefilePath };
            filedic.Add(sourcefilePath, fileResult.Headers?.ContentDisposition?.FileNameStar ?? sourcefilePath);
            //var filePath = sourcefilePath;
            string txtFolder = Guid.NewGuid().ToString("N");
            string desPath = string.Empty;
            if (fileExt.ToLower().Equals(".zip") || fileExt.ToLower().Equals(".rar"))
            {
                desPath = Path.Combine(sourcePath, txtFolder);
                if (!Directory.Exists(desPath))
                {
                    Directory.CreateDirectory(desPath);
                }
                try
                {
                    FileHelper.ExtractToDirectory(sourcefilePath, desPath);
                }
                catch (Exception ex)
                {
                    RemoveTempFile(sourcefilePath, desPath);
                    throw new Exception(string.Format("zip压缩包解压失败,异常信息：{0}", ex.Message));
                }

                DirectoryInfo root = new DirectoryInfo(desPath);
                var fileInfosDic = new Dictionary<string, FileInfo>();
                _adminFileClient.GetFileInfosByDirectoryInfo(fileInfosDic, root);

                if (fileInfosDic.Count().Equals(0))
                {
                    RemoveTempFile(sourcefilePath, desPath);
                    throw new Exception("zip压缩包为空");
                }

                datafiles = fileInfosDic.Keys.ToList();
                filedic = fileInfosDic.Values.ToList().ToDictionary(f => f.FullName, y => y.Name);
            }
            List<string> errmsg = new List<string>();
            foreach (var item in datafiles)
            {
                string filePath = item;
                var cfileExt = Path.GetExtension(filePath).ToLower();
                List<MXCustomerSalesData> tempdata = new List<MXCustomerSalesData>();
                try
                {
                    //if (cfileExt.Equals(".txt"))
                    //    tempdata = await ReadArabAmazonTxtFile(filePath, sourcefilePath, desPath);
                    if (cfileExt.Equals(".csv"))
                    {
                        try
                        {
                            StringBuilder sb = new StringBuilder();
                            Encoding.RegisterProvider(CodePagesEncodingProvider.Instance);
                            using (var reader = new StreamReader(filePath, Encoding.GetEncoding("utf-8")))
                            {
                                string content = "";
                                do
                                {
                                    content = await reader.ReadLineAsync();
                                    content = content.ToLower();
                                } while (content.IndexOf("fecha/hora") < 0 || content.IsNullOrBlank());

                                sb.AppendLine(content);
                                sb.Append(reader.ReadToEnd());
                                var bytes = Encoding.Default.GetBytes(sb.ToString());
                                Stream stream = new MemoryStream(bytes);
                                TextReader treader = new StreamReader(stream);

                                var csvReader = new CsvReader(treader, new CsvHelper.Configuration.CsvConfiguration(new System.Globalization.CultureInfo("zh-CHS"))
                                {
                                    BadDataFound = null,
                                    Delimiter = ",",
                                    HasHeaderRecord = true,
                                });
                                csvReader.Context.RegisterClassMap(typeof(MXCustomerSalesDataMap));
                                tempdata = csvReader.GetRecords<MXCustomerSalesData>().ToList();
                            }
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(ex, $"计算amazon csv格式数据异常：文件：{orifilePath}");
                            RemoveTempFile(sourcefilePath, desPath);
                            throw new Exception("csv格式数据异常");
                        }
                        //RemoveTempFile(sourcefilePath, desPath);
                    }
                    models.AddRange(tempdata);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, $"Amazon销售数据文件读取异常：{ex.Message}");
                    errmsg.Add($"文件【{filedic[item]}】, {ex.Message} ");
                }
            }
            if (errmsg.Any())
            {
                RemoveTempFile(sourcefilePath, desPath);
                throw new Exception($"{string.Join(";", errmsg)}");
            }
            RemoveTempFile(sourcefilePath, desPath);
            return models;
        }
        private async Task<List<CaCustomerSalesData>> GetCaCustomerSalesDatas(string orifilePath)
        {
            List<CaCustomerSalesData> models = new List<CaCustomerSalesData>();
            Dictionary<string, string> filedic = new Dictionary<string, string>();
            string fileExt = Path.GetExtension(orifilePath).ToLower();
            List<string> extTypes = new List<string> { ".zip", ".rar", ".csv" };
            if (!extTypes.Contains(fileExt))
            {
                throw new Exception("非amazon格式的销售数据文件");
            }
            //通过接口获取文件
            var fileResult = await _adminFileClient.GetFileContent(orifilePath);
            var oristream = await fileResult.ReadAsStreamAsync();

            string saveName = Guid.NewGuid().ToString("N") + fileExt;
            var path = Directory.GetCurrentDirectory();
            string sourcePath = Path.Combine(path, "StaticFiles");
            if (!Directory.Exists(sourcePath))
            {
                Directory.CreateDirectory(sourcePath);
            }
            var sourcefilePath = Path.Combine(sourcePath, saveName);
            //保存文件
            using (var fileStream = new FileStream(sourcefilePath, FileMode.Create))
            {
                await oristream.CopyToAsync(fileStream);
            }
            List<string> datafiles = new List<string>() { sourcefilePath };
            filedic.Add(sourcefilePath, fileResult.Headers?.ContentDisposition?.FileNameStar ?? sourcefilePath);
            //var filePath = sourcefilePath;
            string txtFolder = Guid.NewGuid().ToString("N");
            string desPath = string.Empty;
            if (fileExt.ToLower().Equals(".zip") || fileExt.ToLower().Equals(".rar"))
            {
                desPath = Path.Combine(sourcePath, txtFolder);
                if (!Directory.Exists(desPath))
                {
                    Directory.CreateDirectory(desPath);
                }
                try
                {
                    FileHelper.ExtractToDirectory(sourcefilePath, desPath);
                }
                catch (Exception ex)
                {
                    RemoveTempFile(sourcefilePath, desPath);
                    throw new Exception(string.Format("zip压缩包解压失败,异常信息：{0}", ex.Message));
                }

                DirectoryInfo root = new DirectoryInfo(desPath);
                var fileInfosDic = new Dictionary<string, FileInfo>();
                _adminFileClient.GetFileInfosByDirectoryInfo(fileInfosDic, root);

                if (fileInfosDic.Count().Equals(0))
                {
                    RemoveTempFile(sourcefilePath, desPath);
                    throw new Exception("zip压缩包为空");
                }

                datafiles = fileInfosDic.Keys.ToList();
                filedic = fileInfosDic.Values.ToList().ToDictionary(f => f.FullName, y => y.Name);
            }
            List<string> errmsg = new List<string>();
            foreach (var item in datafiles)
            {
                string filePath = item;
                var cfileExt = Path.GetExtension(filePath).ToLower();
                List<CaCustomerSalesData> tempdata = new List<CaCustomerSalesData>();
                try
                {
                    //if (cfileExt.Equals(".txt"))
                    //    tempdata = await ReadArabAmazonTxtFile(filePath, sourcefilePath, desPath);
                    if (cfileExt.Equals(".csv"))
                    {
                        try
                        {
                            StringBuilder sb = new StringBuilder();
                            Encoding.RegisterProvider(CodePagesEncodingProvider.Instance);
                            using (var reader = new StreamReader(filePath, Encoding.GetEncoding("utf-8")))
                            {
                                string content = "";
                                do
                                {
                                    content = await reader.ReadLineAsync();
                                    content = content.ToLower();
                                } while (content.IndexOf("order_date") < 0 || content.IsNullOrBlank());

                                sb.AppendLine(content);
                                sb.Append(reader.ReadToEnd());
                                var bytes = Encoding.Default.GetBytes(sb.ToString());
                                Stream stream = new MemoryStream(bytes);
                                TextReader treader = new StreamReader(stream);

                                var csvReader = new CsvReader(treader, new CsvHelper.Configuration.CsvConfiguration(new System.Globalization.CultureInfo("zh-CHS"))
                                {
                                    BadDataFound = null,
                                    Delimiter = ",",
                                    HasHeaderRecord = true,
                                });
                                csvReader.Context.RegisterClassMap(typeof(CaCustomerSalesDataMap));
                                tempdata = csvReader.GetRecords<CaCustomerSalesData>().ToList();
                            }
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(ex, $"计算amazon csv格式数据异常：文件：{orifilePath}");
                            RemoveTempFile(sourcefilePath, desPath);
                            throw new Exception("csv格式数据异常");
                        }
                        //RemoveTempFile(sourcefilePath, desPath);
                    }
                    models.AddRange(tempdata);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, $"Amazon销售数据文件读取异常：{ex.Message}");
                    errmsg.Add($"文件【{filedic[item]}】, {ex.Message} ");
                }
            }
            if (errmsg.Any())
            {
                RemoveTempFile(sourcefilePath, desPath);
                throw new Exception($"{string.Join(";", errmsg)}");
            }
            RemoveTempFile(sourcefilePath, desPath);
            return models;
        }

        private async Task<List<ArabCustomerSalesData>> GetArabCustomerSalesDatas(string orifilePath)
        {
            List<ArabCustomerSalesData> models = new List<ArabCustomerSalesData>();
            Dictionary<string, string> filedic = new Dictionary<string, string>();
            string fileExt = Path.GetExtension(orifilePath).ToLower();
            //List<string> extTypes = new List<string> { ".txt", ".zip", ".rar",  ".csv" };
            List<string> extTypes = new List<string> { ".zip", ".rar", ".csv", ".txt" };
            if (!extTypes.Contains(fileExt))
            {
                throw new Exception("非amazon格式的销售数据文件");
            }
            //通过接口获取文件
            var fileResult = await _adminFileClient.GetFileContent(orifilePath);
            var oristream = await fileResult.ReadAsStreamAsync();

            string saveName = Guid.NewGuid().ToString("N") + fileExt;
            var path = Directory.GetCurrentDirectory();
            string sourcePath = Path.Combine(path, "StaticFiles");
            if (!Directory.Exists(sourcePath))
            {
                Directory.CreateDirectory(sourcePath);
            }
            var sourcefilePath = Path.Combine(sourcePath, saveName);
            //保存文件
            using (var fileStream = new FileStream(sourcefilePath, FileMode.Create))
            {
                await oristream.CopyToAsync(fileStream);
            }
            List<string> datafiles = new List<string>() { sourcefilePath };
            filedic.Add(sourcefilePath, fileResult.Headers?.ContentDisposition?.FileNameStar ?? sourcefilePath);
            //var filePath = sourcefilePath;
            string txtFolder = Guid.NewGuid().ToString("N");
            string desPath = string.Empty;
            if (fileExt.ToLower().Equals(".zip") || fileExt.ToLower().Equals(".rar"))
            {
                desPath = Path.Combine(sourcePath, txtFolder);
                if (!Directory.Exists(desPath))
                {
                    Directory.CreateDirectory(desPath);
                }
                try
                {
                    FileHelper.ExtractToDirectory(sourcefilePath, desPath);
                }
                catch (Exception ex)
                {
                    RemoveTempFile(sourcefilePath, desPath);
                    throw new Exception(string.Format("zip压缩包解压失败,异常信息：{0}", ex.Message));
                }

                DirectoryInfo root = new DirectoryInfo(desPath);
                var fileInfosDic = new Dictionary<string, FileInfo>();
                _adminFileClient.GetFileInfosByDirectoryInfo(fileInfosDic, root);

                if (fileInfosDic.Count().Equals(0))
                {
                    RemoveTempFile(sourcefilePath, desPath);
                    throw new Exception("zip压缩包为空");
                }

                datafiles = fileInfosDic.Keys.ToList();
                filedic = fileInfosDic.Values.ToList().ToDictionary(f => f.FullName, y => y.Name);
            }
            List<string> errmsg = new List<string>();
            foreach (var item in datafiles)
            {
                string filePath = item;
                var cfileExt = Path.GetExtension(filePath).ToLower();
                List<ArabCustomerSalesData> tempdata = new List<ArabCustomerSalesData>();
                try
                {
                    if (cfileExt.Equals(".txt"))
                        tempdata = await ReadArabAmazonTxtFile(filePath, sourcefilePath, desPath);
                    else if (cfileExt.Equals(".csv"))
                    {
                        try
                        {
                            StringBuilder sb = new StringBuilder();
                            Encoding.RegisterProvider(CodePagesEncodingProvider.Instance);
                            using (var reader = new StreamReader(filePath, Encoding.GetEncoding("utf-8")))
                            {
                                string content = "";
                                do
                                {
                                    content = await reader.ReadLineAsync();
                                    content = content.ToLower();
                                } while (content.IndexOf("date/time") < 0 || content.IsNullOrBlank());

                                sb.AppendLine(content);
                                sb.Append(reader.ReadToEnd());
                                var bytes = Encoding.Default.GetBytes(sb.ToString());
                                Stream stream = new MemoryStream(bytes);
                                TextReader treader = new StreamReader(stream);

                                var csvReader = new CsvReader(treader, new CsvHelper.Configuration.CsvConfiguration(new System.Globalization.CultureInfo("zh-CHS"))
                                {
                                    BadDataFound = null,
                                    Delimiter = ",",
                                    HasHeaderRecord = true,
                                });
                                csvReader.Context.RegisterClassMap(typeof(ArabCustomerSalesDataMap));
                                tempdata = csvReader.GetRecords<ArabCustomerSalesData>().ToList();
                            }
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(ex, $"计算amazon csv格式数据异常：文件：{orifilePath}");
                            RemoveTempFile(sourcefilePath, desPath);
                            throw new Exception("csv格式数据异常");
                        }
                        //RemoveTempFile(sourcefilePath, desPath);
                    }
                    models.AddRange(tempdata);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, $"Amazon销售数据文件读取异常：{ex.Message}");
                    errmsg.Add($"文件【{filedic[item]}】, {ex.Message} ");
                }
            }
            if (errmsg.Any())
            {
                RemoveTempFile(sourcefilePath, desPath);
                throw new Exception($"{string.Join(";", errmsg)}");
            }
            RemoveTempFile(sourcefilePath, desPath);
            return models;
        }
        /// <summary>
        /// 计算发票金额
        /// </summary>
        /// <param name="invoicefile"></param>
        /// <returns></returns>
        /// <exception cref="VatCommonException"></exception>
        public async Task<ArabInvoiceData> GetArabInvoiceAmount(ArabInvoiceParam param)
        {
            string invoicefile = param.FilePath;
            //return 100;
            decimal total = 0;
            var invoiceitems = new List<InvoiceItem>();
            ArabInvoiceData data = new ArabInvoiceData()
            {
                TotalExclAmount = 0,
                InvoiceItems = new List<InvoiceItem>()
            };
            Dictionary<string, string> filedic = new Dictionary<string, string>();
            #region 发票文件集合

            string fileExt = Path.GetExtension(invoicefile).ToLower();
            List<string> extTypes = new List<string> { ".zip", ".rar", ".pdf" };
            if (!extTypes.Contains(fileExt))
            {
                throw new VatCommonException("FileError", "文件格式不正确");
            }
            //通过接口获取文件
            var fileResult = await _adminFileClient.GetFileContent(invoicefile);
            var oristream = await fileResult.ReadAsStreamAsync();

            string saveName = Guid.NewGuid().ToString("N") + fileExt;
            var path = Directory.GetCurrentDirectory();
            string sourcePath = Path.Combine(path, "StaticFiles");
            if (!Directory.Exists(sourcePath))
            {
                Directory.CreateDirectory(sourcePath);
            }
            var sourcefilePath = Path.Combine(sourcePath, saveName);
            //保存文件
            using (var fileStream = new FileStream(sourcefilePath, FileMode.Create))
            {
                await oristream.CopyToAsync(fileStream);
            }
            List<string> datafiles = new List<string>() { sourcefilePath };
            filedic.Add(sourcefilePath, fileResult.Headers?.ContentDisposition?.FileNameStar ?? sourcefilePath);
            //var filePath = sourcefilePath;
            string txtFolder = Guid.NewGuid().ToString("N");
            string desPath = string.Empty;
            if (fileExt.ToLower().Equals(".zip") || fileExt.ToLower().Equals(".rar"))
            {
                desPath = Path.Combine(sourcePath, txtFolder);
                if (!Directory.Exists(desPath))
                {
                    Directory.CreateDirectory(desPath);
                }
                try
                {
                    FileHelper.ExtractToDirectory(sourcefilePath, desPath);
                }
                catch (Exception ex)
                {
                    RemoveTempFile(sourcefilePath, desPath);
                    throw new VatCommonException("UnzipError", string.Format("zip压缩包解压失败,异常信息：{0}", ex.Message));
                }

                DirectoryInfo root = new DirectoryInfo(desPath);
                var fileInfosDic = new Dictionary<string, FileInfo>();
                _adminFileClient.GetFileInfosByDirectoryInfo(fileInfosDic, root);

                if (fileInfosDic.Count().Equals(0))
                {
                    RemoveTempFile(sourcefilePath, desPath);
                    throw new VatCommonException("FileEmpty", "zip压缩包为空");
                }

                datafiles = fileInfosDic.Keys.ToList();
                filedic = fileInfosDic.Values.ToList().ToDictionary(f => f.FullName, y => y.Name);
            }
            #endregion

            List<string> errmsg = new List<string>();
            foreach (var item in datafiles)
            {
                string filePath = item;
                try
                {
                    total += ResolveAsposePdf(filePath, invoiceitems, param.CountryCode, filedic[item]);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, $"Amazon销售数据文件读取异常：{ex.Message}");
                    errmsg.Add($"文件【{filedic[item]}】, {ex.Message} ");
                }
            }
            data.TotalExclAmount = total;
            data.InvoiceItems = invoiceitems;
            //if (errmsg.Any())
            //{
            //    RemoveTempFile(sourcefilePath, desPath);
            //    throw new VatCommonException("FileEmpty", $"{errmsg}");
            //}
            RemoveTempFile(sourcefilePath, desPath);
            return data;
        }
        private decimal ResolvePdf(string file, List<InvoiceItem> invoiceItems, string code)
        {
            decimal total = 0;
            //加载PDF文档
            using (PdfDocument pdf = new PdfDocument())
            {
                pdf.LoadFromFile(file);
                StringBuilder builder = new StringBuilder();

                //抽取表格
                PdfTableExtractor extractor = new PdfTableExtractor(pdf);
                PdfTable[] tableLists = null;
                if (code == "SA")
                {
                    for (int pageIndex = 0; pageIndex < pdf.Pages.Count; pageIndex++)
                    {
                        tableLists = extractor.ExtractTable(pageIndex);
                        if (tableLists != null && tableLists.Length > 0)
                        {
                            foreach (PdfTable table in tableLists)
                            {
                                string flag = table.GetText(0, 0);
                                if (flag.ToLower().Contains("date of transaction"))
                                {
                                    string price = table.GetText(table.GetRowCount() - 1, 2);
                                    decimal tmp = 0;
                                    decimal.TryParse(price.Replace("SAR", "").Trim(), out tmp);
                                    return tmp;
                                }
                            }
                        }
                    }
                }
                else if (code == "AE")
                {
                    string vatrate = "";
                    string vatratetype = "";
                    for (int pageIndex = 0; pageIndex < pdf.Pages.Count; pageIndex++)
                    {
                        tableLists = extractor.ExtractTable(pageIndex);

                        if (tableLists != null && tableLists.Length > 0)
                        {
                            if (pageIndex == 0)
                            {
                                vatrate = tableLists.First().GetText(1, 3);
                                vatratetype = vatrate == "5.00%" ? "FivePercent" : "ZeroPercent";
                            }
                            foreach (PdfTable table in tableLists)
                            {
                                int lastrow = table.GetRowCount() - 1;
                                string flag1 = table.GetText(lastrow, 0);
                                string flag2 = table.GetText(lastrow, 1);
                                if (flag1.IsNullOrBlank() && flag2.ToLower().Equals("total"))
                                {
                                    string price = table.GetText(table.GetRowCount() - 1, 2);
                                    decimal tmp = 0;
                                    decimal.TryParse(price.Replace("AED", "").Trim(), out tmp);
                                    var item = invoiceItems.FirstOrDefault(i => i.Type == vatratetype);
                                    if (item == null)
                                        invoiceItems.Add(new InvoiceItem { Type = vatratetype, Amount = tmp });
                                    else
                                        item.Amount += tmp;
                                    return tmp;
                                }
                            }
                        }
                    }
                }
            }
            return total;
        }
        private decimal ResolveAsposePdf(string file, List<InvoiceItem> invoiceItems, string code, string filename)
        {
            decimal total = 0;
            var doc = new Aspose.Pdf.Document(file);

            // Create TableAbsorber object to find tables
            var absorber = new Aspose.Pdf.Text.TableAbsorber();
            PdfDocument pdf = new PdfDocument();
            pdf.LoadFromFile(file);
            string text = pdf.Pages[0].ExtractText();
            bool isCreditNote = text.Contains("TAX CREDIT NOTE");
            // Visit first page with absorber
            //absorber.Visit(doc.Pages[1]);
            bool isHasTax = false;
            if (code == "SA")
            {
                foreach (var page in doc.Pages)
                {
                    absorber.Visit(page);
                    foreach (var table in absorber.TableList)
                    {
                        if (isCreditNote)
                        {
                            if (!isHasTax)
                            {
                                string vatrate = GetAsposePdfTableCellText(table, 1, 4);
                                if (!vatrate.Contains("15"))
                                    return 0;
                                isHasTax = true;
                            }
                            string lastrow_cell1 = GetAsposePdfTableCellText(table, table.RowList.Count - 1, 0);
                            if (lastrow_cell1.Contains("Total Adjustments"))
                            {
                                string price = GetAsposePdfTableCellText(table, table.RowList.Count - 1, 1);
                                decimal tmp = 0;
                                decimal.TryParse(price.Replace("SAR", "").Trim().Replace(" ", ""), out tmp);
                                if (tmp > 0)
                                    invoiceItems.Add(new InvoiceItem { Type = "", Amount = tmp, Name = filename });
                                return tmp;
                            }
                        }
                        else
                        {
                            string vatrate = GetAsposePdfTableCellText(table, 1, 3);
                            if (vatrate == "0.00%")
                                return 0;
                            string lastrow_cell1 = GetAsposePdfTableCellText(table, table.RowList.Count - 1, 0);
                            string lastrow_cell2 = GetAsposePdfTableCellText(table, table.RowList.Count - 1, 1);

                            //string flag = GetAsposePdfTableCellText(table, 0, 0);
                            //if (flag.ToLower().Contains("date of transaction"))
                            if (lastrow_cell1.IsNullOrBlank() && lastrow_cell2.Contains("Total"))
                            {
                                string price = GetAsposePdfTableCellText(table, table.RowList.Count - 1, 2);
                                decimal tmp = 0;
                                decimal.TryParse(price.Replace("SAR", "").Trim(), out tmp);
                                if (tmp > 0)
                                    invoiceItems.Add(new InvoiceItem { Type = "", Amount = tmp, Name = filename });
                                return tmp;
                            }
                        }
                    }
                }
            }
            else if (code == "AE")
            {
                string vatrate = "";
                string vatratetype = "";
                foreach (var page in doc.Pages)
                {
                    absorber.Visit(page);
                    foreach (var table in absorber.TableList)
                    {
                        if (isCreditNote)
                        {
                            if (!isHasTax)
                            {
                                vatrate = GetAsposePdfTableCellText(table, 1, 4);
                                if (!vatrate.Contains("5"))
                                    return 0;
                                isHasTax = true;
                            }
                            string lastrow_cell1 = GetAsposePdfTableCellText(table, table.RowList.Count - 1, 0);
                            if (lastrow_cell1.Contains("Total Adjustments"))
                            {
                                string price = GetAsposePdfTableCellText(table, table.RowList.Count - 1, 1);
                                decimal tmp = 0;
                                decimal.TryParse(price.Replace("AED", "").Trim().Replace(" ", ""), out tmp);
                                if (tmp != 0)
                                    invoiceItems.Add(new InvoiceItem { Type = vatratetype, Amount = tmp });

                                return tmp;
                            }
                        }
                        else
                        {
                            if (vatrate.IsNullOrBlank())
                            {
                                vatrate = GetAsposePdfTableCellText(table, 1, 3);
                                vatratetype = vatrate == "5.00%" ? "FivePercent" : "ZeroPercent";
                                if (vatratetype == "ZeroPercent")
                                    return 0;
                            }
                            int lastrow = table.RowList.Count - 1;
                            string flag1 = GetAsposePdfTableCellText(table, lastrow, 0);
                            string flag2 = GetAsposePdfTableCellText(table, lastrow, 1);
                            if (flag1.IsNullOrBlank() && flag2.ToLower().Equals("total"))
                            {
                                string price = GetAsposePdfTableCellText(table, lastrow, 2);
                                decimal tmp = 0;
                                decimal.TryParse(price.Replace("AED", "").Trim(), out tmp);
                                if (tmp > 0)
                                    invoiceItems.Add(new InvoiceItem { Type = vatratetype, Amount = tmp, Name = filename });
                                return tmp;
                            }
                        }
                    }
                }
            }
            return total;
        }
        private string GetAsposePdfTableCellText(Aspose.Pdf.Text.AbsorbedTable table, int row, int cell)
        {
            var frages = table.RowList[row].CellList[cell].TextFragments;
            return frages.Any() ? frages[1].Text : "";
        }

        /// <summary>
        /// 删除临时文件，文件夹
        /// </summary>
        /// <param name="sourcefilePath"></param>
        /// <param name="desPath"></param>
        private void RemoveTempFile(string sourcefilePath, string desPath)
        {
            if (File.Exists(sourcefilePath))
            {
                File.Delete(sourcefilePath);
            }
            if (!string.IsNullOrEmpty(desPath))
            {
                DeleteDir(desPath);
            }
        }

        private List<CustomerSalesData> ReadAmazonTxtFile1(string filePath)
        {
            List<CustomerSalesData> models = new List<CustomerSalesData>();
            var fileHeaderDic = new Dictionary<string, int>();
            var file = System.IO.File.Open(filePath, FileMode.Open);
            List<string> txtList = new List<string>();
            int sqn = 0;
            int headerSplitCnt = 0;
            var heardError = string.Empty;
            var index_ACTIVITY_PERIOD = 0;
            var index_TRANSACTION_TYPE = 0;
            var index_TOTAL_ACTIVITY_VALUE_AMT_VAT_INCL = 0;
            var index_TRANSACTION_CURRENCY_CODE = 0;
            var index_SALE_DEPART_COUNTRY = 0;
            var index_BUYER_VAT_NUMBER = 0;
            var index_SALE_ARRIVAL_COUNTRY = 0;
            var index_PRICE_OF_ITEMS_VAT_RATE_PERCENT = 0;
            var index_TRANSACTION_COMPLETE_DATE = 0;
            var index_MARKETPLACE = 0;
            var index_TOTAL_ACTIVITY_VALUE_AMT_VAT_EXCL = 0;
            var index_TOTAL_ACTIVITY_VALUE_VAT_AMT = 0;
            var index_TAX_COLLECTION_RESPONSIBILITY = 0;
            var index_BUYER_VAT_NUMBER_COUNTRY = 0;
            var index_TAX_CALCULATION_DATE = 0;
            var index_TAXABLE_JURISDICTION = 0;
            var index_SELLER_DEPART_VAT_NUMBER_COUNTRY = 0;
            using (var stream = new StreamReader(file, System.Text.Encoding.UTF8))
            {
                while (!stream.EndOfStream)
                {
                    var str = stream.ReadLine();
                    if (++sqn == 1)
                    {
                        var tempHeaderList = str.Split('\t').ToList();
                        headerSplitCnt = tempHeaderList.Count();
                        index_ACTIVITY_PERIOD = tempHeaderList.IndexOf("ACTIVITY_PERIOD");
                        if (index_ACTIVITY_PERIOD == -1) heardError += "表头找不到字段：ACTIVITY_PERIOD;";
                        index_TRANSACTION_TYPE = tempHeaderList.IndexOf("TRANSACTION_TYPE");
                        if (index_TRANSACTION_TYPE == -1) heardError += "表头找不到字段：TRANSACTION_TYPE;";
                        index_TOTAL_ACTIVITY_VALUE_AMT_VAT_INCL = tempHeaderList.IndexOf("TOTAL_ACTIVITY_VALUE_AMT_VAT_INCL");
                        if (index_TOTAL_ACTIVITY_VALUE_AMT_VAT_INCL == -1) heardError += "表头找不到字段：TOTAL_ACTIVITY_VALUE_AMT_VAT_INCL;";
                        index_TRANSACTION_CURRENCY_CODE = tempHeaderList.IndexOf("TRANSACTION_CURRENCY_CODE");
                        if (index_TRANSACTION_CURRENCY_CODE == -1) heardError += "表头找不到字段：TRANSACTION_CURRENCY_CODE;";
                        index_SALE_DEPART_COUNTRY = tempHeaderList.IndexOf("SALE_DEPART_COUNTRY");
                        if (index_SALE_DEPART_COUNTRY == -1) heardError += "表头找不到字段：SALE_DEPART_COUNTRY;";
                        index_SALE_ARRIVAL_COUNTRY = tempHeaderList.IndexOf("SALE_ARRIVAL_COUNTRY");
                        if (index_SALE_ARRIVAL_COUNTRY == -1) heardError += "表头找不到字段：SALE_ARRIVAL_COUNTRY;";
                        index_BUYER_VAT_NUMBER = tempHeaderList.IndexOf("BUYER_VAT_NUMBER");
                        //if (index_BUYER_VAT_NUMBER == -1) heardError += "表头找不到字段：BUYER_VAT_NUMBER;";
                        index_PRICE_OF_ITEMS_VAT_RATE_PERCENT = tempHeaderList.IndexOf("PRICE_OF_ITEMS_VAT_RATE_PERCENT");
                        index_TAX_COLLECTION_RESPONSIBILITY = tempHeaderList.IndexOf("TAX_COLLECTION_RESPONSIBILITY");
                        index_BUYER_VAT_NUMBER_COUNTRY = tempHeaderList.IndexOf("BUYER_VAT_NUMBER_COUNTRY");
                        if (index_PRICE_OF_ITEMS_VAT_RATE_PERCENT == -1) heardError += "表头找不到字段：PRICE_OF_ITEMS_VAT_RATE_PERCENT;";
                        //if (isGBFrs)
                        //{
                        index_TRANSACTION_COMPLETE_DATE = tempHeaderList.IndexOf("TRANSACTION_COMPLETE_DATE");
                        //    if (index_TRANSACTION_COMPLETE_DATE == -1) heardError += "表头找不到字段：TRANSACTION_COMPLETE_DATE;";
                        //}
                        index_TAX_CALCULATION_DATE = tempHeaderList.IndexOf("TAX_CALCULATION_DATE");
                        index_MARKETPLACE = tempHeaderList.IndexOf("MARKETPLACE");
                        index_TOTAL_ACTIVITY_VALUE_AMT_VAT_EXCL = tempHeaderList.IndexOf("TOTAL_ACTIVITY_VALUE_AMT_VAT_EXCL");
                        index_TOTAL_ACTIVITY_VALUE_VAT_AMT = tempHeaderList.IndexOf("TOTAL_ACTIVITY_VALUE_VAT_AMT");
                        index_TAXABLE_JURISDICTION = tempHeaderList.IndexOf("TAXABLE_JURISDICTION");
                        index_SELLER_DEPART_VAT_NUMBER_COUNTRY = tempHeaderList.IndexOf("SELLER_DEPART_VAT_NUMBER_COUNTRY");
                        if (!string.IsNullOrEmpty(heardError))
                        {
                            //throw new VatCommonException(Error.Codes.AmazonVatSaleamountCalculateError,
                            //string.Format(Error.Names.AmazonVatSaleamountCalculateError, heardError));
                            throw new Exception(heardError);
                        }
                    }
                    else
                    {
                        txtList.Add(str);
                    }
                }
            }
            file.Close();
            if (txtList.Count == 0)
            {
                //throw new VatCommonException(Error.Codes.AmazonVatSaleamountCalculateError,
                //string.Format(Error.Names.AmazonVatSaleamountCalculateError, "文件无有效列"));
                throw new Exception("文件无有效列");
            }
            sqn = 0;
            var errorMsg = string.Empty;
            var splitError = string.Empty;
            var decimalError = string.Empty;
            //循环list
            foreach (string item in txtList)
            {
                ++sqn;
                string[] tempstr = item.Split('\t');
                if (tempstr.Length != headerSplitCnt)
                {
                    splitError += sqn + "、";
                    continue;
                }
                CustomerSalesData model = new CustomerSalesData();
                model.ACTIVITY_PERIOD = tempstr[index_ACTIVITY_PERIOD];//string
                model.TRANSACTION_TYPE = tempstr[index_TRANSACTION_TYPE];//string
                string tempValue = tempstr[index_TOTAL_ACTIVITY_VALUE_AMT_VAT_INCL];//decimal
                if (string.IsNullOrEmpty(tempValue))
                {
                    tempValue = "0";
                }
                else
                {
                    try
                    {
                        decimal.Parse(tempValue);
                    }
                    catch
                    {

                        decimalError += sqn + "、";
                        continue;
                    }
                }
                model.TOTAL_ACTIVITY_VALUE_AMT_VAT_INCL = tempValue;//decimal
                model.TRANSACTION_CURRENCY_CODE = tempstr[index_TRANSACTION_CURRENCY_CODE];//string
                model.SALE_DEPART_COUNTRY = tempstr[index_SALE_DEPART_COUNTRY];//string
                model.SALE_ARRIVAL_COUNTRY = tempstr[index_SALE_ARRIVAL_COUNTRY];//string
                if (index_BUYER_VAT_NUMBER != -1)
                    model.BUYER_VAT_NUMBER = tempstr[index_BUYER_VAT_NUMBER];//string
                model.PRICE_OF_ITEMS_VAT_RATE_PERCENT = tempstr[index_PRICE_OF_ITEMS_VAT_RATE_PERCENT];
                model.MARKETPLACE = tempstr[index_MARKETPLACE];
                if (index_TAX_COLLECTION_RESPONSIBILITY != -1)
                    model.TAX_COLLECTION_RESPONSIBILITY = tempstr[index_TAX_COLLECTION_RESPONSIBILITY];
                if (index_BUYER_VAT_NUMBER_COUNTRY != -1)
                    model.BUYER_VAT_NUMBER_COUNTRY = tempstr[index_BUYER_VAT_NUMBER_COUNTRY];
                if (index_TAXABLE_JURISDICTION != -1)
                    model.TAXABLE_JURISDICTION = tempstr[index_TAXABLE_JURISDICTION];
                if (index_SELLER_DEPART_VAT_NUMBER_COUNTRY != -1)
                    model.SELLER_DEPART_VAT_NUMBER_COUNTRY = tempstr[index_SELLER_DEPART_VAT_NUMBER_COUNTRY];
                string exclValue = tempstr[index_TOTAL_ACTIVITY_VALUE_AMT_VAT_EXCL];//decimal
                if (string.IsNullOrEmpty(exclValue))
                {
                    exclValue = "0";
                }
                else
                {
                    try
                    {
                        decimal.Parse(exclValue);
                    }
                    catch
                    {

                        decimalError += sqn + "、";
                        continue;
                    }
                }
                model.TOTAL_ACTIVITY_VALUE_AMT_VAT_EXCL = exclValue;//decimal
                string amtValue = tempstr[index_TOTAL_ACTIVITY_VALUE_VAT_AMT];//decimal
                if (string.IsNullOrEmpty(amtValue) || amtValue == "0.0")
                {
                    amtValue = "0";
                }
                else
                {
                    try
                    {
                        decimal.Parse(amtValue);
                    }
                    catch
                    {

                        decimalError += sqn + "、";
                        continue;
                    }
                }
                model.TOTAL_ACTIVITY_VALUE_VAT_AMT = amtValue;//decimal
                if (index_TRANSACTION_COMPLETE_DATE != -1)
                {
                    var transactionCompleteDate = tempstr[index_TRANSACTION_COMPLETE_DATE];
                    if (string.IsNullOrEmpty(transactionCompleteDate))
                    {
                        model.TRANSACTION_COMPLETE_DATE = DateTime.MinValue;
                    }
                    else
                    {
                        try
                        {
                            if (transactionCompleteDate.Contains("/"))
                            {
                                var dateSplit = transactionCompleteDate.Split('/');
                                model.TRANSACTION_COMPLETE_DATE = DateTime.Parse(dateSplit[2] + "-" + dateSplit[1] + "-" + dateSplit[0]);
                            }
                            else if (transactionCompleteDate.Contains("-"))
                            {
                                var dateSplit = transactionCompleteDate.Split('-');
                                model.TRANSACTION_COMPLETE_DATE = DateTime.Parse(dateSplit[2] + "-" + dateSplit[1] + "-" + dateSplit[0]);//DateTime
                            }
                        }
                        catch
                        {

                            //dateTimeError += sqn + "、";
                            continue;
                        }
                    }
                }
                if (index_TAX_CALCULATION_DATE != -1)
                {
                    var taxCalculationDate = tempstr[index_TAX_CALCULATION_DATE];
                    if (string.IsNullOrEmpty(taxCalculationDate))
                    {
                        model.TAX_CALCULATION_DATE = DateTime.MinValue;
                    }
                    else
                    {
                        try
                        {
                            if (taxCalculationDate.Contains("/"))
                            {
                                var dateSplit = taxCalculationDate.Split('/');
                                model.TAX_CALCULATION_DATE = DateTime.Parse(dateSplit[2] + "-" + dateSplit[1] + "-" + dateSplit[0]);//DateTime
                            }
                            else if (taxCalculationDate.Contains("-"))
                            {
                                var dateSplit = taxCalculationDate.Split('-');
                                model.TAX_CALCULATION_DATE = DateTime.Parse(dateSplit[2] + "-" + dateSplit[1] + "-" + dateSplit[0]);//DateTime
                            }
                        }
                        catch
                        {
                            continue;
                        }
                    }
                }
                models.Add(model);
            }

            if (!string.IsNullOrEmpty(splitError) || !string.IsNullOrEmpty(decimalError))
            {
                splitError = string.IsNullOrEmpty(splitError) ? string.Empty
                    : string.Format("第{0}行，数据列数与表头列数不一致！\t\n", splitError.TrimEnd('、'));
                decimalError = string.IsNullOrEmpty(decimalError) ? string.Empty
                    : string.Format("第{0}行，TOTAL_ACTIVITY_VALUE_AMT_VAT_INCL不为数值！\t\n", decimalError.TrimEnd('、'));

                errorMsg = splitError + decimalError;
                //throw new VatCommonException(Error.Codes.AmazonVatSaleamountCalculateError,
                // string.Format(Error.Names.AmazonVatSaleamountCalculateError, errorMsg));
                throw new Exception(errorMsg);
            }
            return models;
        }

        private async Task<List<CustomerSalesData>> ReadAmazonExcelFile(string filepath, string sourcefilePath, string desPath)
        {
            List<CustomerSalesData> data = new List<CustomerSalesData>();
            #region NPOI
            var excelHelper = new Util.Excel.ExcelHelper<CustomerSalesData>();
            var result = await excelHelper.GetExcelData(filepath);
            #endregion
            RemoveTempFile(sourcefilePath, desPath);
            #region 异常数据提示
            if (result.HasError)
            {
                if (result.Exception != null)
                {
                    throw new Exception(result.Exception.Message, result.Exception);
                }
                if (result.TemplateErrors.Count > 0)
                {
                    if (result.TemplateErrors.Any(e => e.Message.Contains("列头重复")))
                    {
                        //列头重复
                        var errors = result.TemplateErrors.Where(e => e.Message.Contains("列头重复")).ToList();
                        string errormsg = $"{string.Join(System.Environment.NewLine, errors.Select(e => $"{e.Message}"))}";
                        throw new Exception(errormsg);
                    }
                    if (result.TemplateErrors.Any(e => e.Message.Contains("当前导入模板中未找到此字段")))
                    {
                        //当前导入模板中未找到次字段，请下载销售模板，按销售模板填充数据后上传
                        var errors = result.TemplateErrors.Where(e => e.Message.Contains("当前导入模板中未找到此字段")).ToList();
                        string errormsg = $"{string.Join(System.Environment.NewLine, errors.Select(e => $"{e.RequireColumnName}:{e.Message}"))}";
                        throw new Exception($"{errormsg}，请下载销售模板，按销售模板填充数据后上传");
                    }
                }
                var rowErrors = result.RowErrors.ToList();
                if (rowErrors.Count() > 0)
                {
                    string errormsg = $"{string.Join(System.Environment.NewLine, rowErrors.Take(10).Select(e => $"第{e.RowIndex}行:{string.Join(",", e.FieldErrors.Select(f => f.Value))}"))}";
                    throw new Exception(errormsg);
                }
            }
            #endregion
            return result.Data.ToList();
        }
        private async Task<List<CustomerSalesData>> ReadAmazonTxtFile(string filepath, string sourcefilePath, string desPath)
        {
            List<CustomerSalesData> data = new List<CustomerSalesData>();
            #region NPOI
            var txtHelper = new TxtHelper<CustomerSalesData>();
            var result = await txtHelper.GetTxtData(filepath);

            #endregion
            //RemoveTempFile(sourcefilePath, desPath);
            #region 异常数据提示
            if (result.HasError)
            {
                if (result.TemplateErrors.Count > 0)
                {
                    if (result.TemplateErrors.Any(e => e.Message.Contains("列头重复")))
                    {
                        //列头重复
                        var errors = result.TemplateErrors.Where(e => e.Message.Contains("列头重复")).ToList();
                        string errormsg = $"{string.Join(System.Environment.NewLine, errors.Select(e => $"{e.Message}"))}";
                        throw new Exception(errormsg);
                    }
                    if (result.TemplateErrors.Any(e => e.Message.Contains("当前导入模板中未找到此字段")))
                    {
                        //当前导入模板中未找到次字段，请下载销售模板，按销售模板填充数据后上传
                        var errors = result.TemplateErrors.Where(e => e.Message.Contains("当前导入模板中未找到此字段")).ToList();
                        string errormsg = $"{string.Join(System.Environment.NewLine, errors.Select(e => $"{e.ColumnName}:{e.Message}"))}";
                        throw new Exception($"{errormsg}，请下载销售模板，按销售模板填充数据后上传");
                    }
                }
                //var rowErrors = result.RowErrors.ToList();
                var rowErrors = result.RowErrors.Where(r => !r.FieldErrors.Any(f => f.Key == "TAX_CALCULATION_DATE"));
                if (rowErrors.Count() > 0)
                {
                    string errormsg = $"{string.Join(System.Environment.NewLine, rowErrors.Take(10).Select(e => $"第{e.RowIndex}行:{string.Join(",", e.FieldErrors.Select(f => f.Value))}"))}";
                    throw new Exception(errormsg);
                }
            }
            #endregion
            return result.Data.ToList();
        }
        private async Task<List<ArabCustomerSalesData>> ReadArabAmazonTxtFile(string filepath, string sourcefilePath, string desPath)
        {
            List<ArabCustomerSalesData> data = new List<ArabCustomerSalesData>();
            #region NPOI
            var txtHelper = new TxtHelper<ArabCustomerSalesData>();
            var result = await txtHelper.GetTxtData(filepath);

            #endregion
            //RemoveTempFile(sourcefilePath, desPath);
            #region 异常数据提示
            if (result.HasError)
            {
                if (result.TemplateErrors.Count > 0)
                {
                    if (result.TemplateErrors.Any(e => e.Message.Contains("列头重复")))
                    {
                        //列头重复
                        var errors = result.TemplateErrors.Where(e => e.Message.Contains("列头重复")).ToList();
                        string errormsg = $"{string.Join(System.Environment.NewLine, errors.Select(e => $"{e.Message}"))}";
                        throw new Exception(errormsg);
                    }
                    if (result.TemplateErrors.Any(e => e.Message.Contains("当前导入模板中未找到此字段")))
                    {
                        //当前导入模板中未找到次字段，请下载销售模板，按销售模板填充数据后上传
                        var errors = result.TemplateErrors.Where(e => e.Message.Contains("当前导入模板中未找到此字段")).ToList();
                        string errormsg = $"{string.Join(System.Environment.NewLine, errors.Select(e => $"{e.ColumnName}:{e.Message}"))}";
                        throw new Exception($"{errormsg}，请下载销售模板，按销售模板填充数据后上传");
                    }
                }
                //var rowErrors = result.RowErrors.ToList();
                var rowErrors = result.RowErrors.Where(r => !r.FieldErrors.Any(f => f.Key == "order city"));
                if (rowErrors.Count() > 0)
                {
                    string errormsg = $"{string.Join(System.Environment.NewLine, rowErrors.Take(10).Select(e => $"第{e.RowIndex}行:{string.Join(",", e.FieldErrors.Select(f => f.Value))}"))}";
                    throw new Exception(errormsg);
                }
            }
            #endregion
            return result.Data.ToList();
        }
        private async Task<List<EBaySalesData>> GeteBaySalesData(string ebayFilePath)
        {
            List<EBaySalesData> data = new List<EBaySalesData>();
            string fileExt = Path.GetExtension(ebayFilePath);
            if (fileExt.ToLower() != ".csv" && fileExt.ToLower() != ".zip" && fileExt.ToLower() != ".rar")
            {
                //throw new VatCommonException(Error.Codes.AmazonVatSaleamountCalculateError,
                //string.Format(Error.Names.AmazonVatSaleamountCalculateError, "非ebay格式的csv或zip文件"));
                throw new Exception("非ebay格式的csv或zip文件");
            }
            //通过接口获取文件
            var fileResult = await _adminFileClient.GetFile(ebayFilePath);

            string saveName = Guid.NewGuid().ToString("N") + fileExt;
            var path = Directory.GetCurrentDirectory();
            string sourcePath = Path.Combine(path, "StaticFiles");
            if (!Directory.Exists(sourcePath))
            {
                Directory.CreateDirectory(sourcePath);
            }
            var sourcefilePath = Path.Combine(sourcePath, saveName);
            //保存文件
            using (var fileStream = new FileStream(sourcefilePath, FileMode.Create))
            {
                await fileResult.CopyToAsync(fileStream);
            }

            var filePath = sourcefilePath;
            string txtFolder = Guid.NewGuid().ToString("N");
            string desPath = string.Empty;
            if (fileExt.ToLower().Equals(".zip") || fileExt.ToLower().Equals(".rar"))
            {
                desPath = Path.Combine(sourcePath, txtFolder);
                if (!Directory.Exists(desPath))
                {
                    Directory.CreateDirectory(desPath);
                }
                try
                {
                    FileHelper.ExtractToDirectory(sourcefilePath, desPath);
                    //System.IO.Compression.ZipFile.ExtractToDirectory(sourcefilePath, desPath);
                }
                catch (Exception ex)
                {
                    //throw new VatCommonException(Error.Codes.AmazonVatSaleamountCalculateError,
                    //    string.Format("zip压缩包解压失败,异常信息：{0}", ex.Message));
                    RemoveTempFile(sourcefilePath, desPath);
                    throw new Exception(string.Format("zip压缩包解压失败,异常信息：{0}", ex.Message));
                }

                DirectoryInfo root = new DirectoryInfo(desPath);
                System.IO.FileInfo[] files = root.GetFiles();
                if (files.Count().Equals(0))
                {
                    //throw new VatCommonException(Error.Codes.AmazonVatSaleamountCalculateError, "zip压缩包为空");
                    RemoveTempFile(sourcefilePath, desPath);
                    throw new Exception("zip压缩包为空");
                }
                else if (files.Count() > 1)
                {
                    //throw new VatCommonException(Error.Codes.AmazonVatSaleamountCalculateError, "zip压缩包内文件数量大于1");
                    RemoveTempFile(sourcefilePath, desPath);
                    throw new Exception("zip压缩包内文件数量大于1");
                }
                filePath = files[0].FullName;
            }
            try
            {
                StringBuilder sb = new StringBuilder();
                Encoding.RegisterProvider(CodePagesEncodingProvider.Instance);
                using (var reader = new StreamReader(filePath, Encoding.GetEncoding("gb2312")))
                {
                    string content = reader.ReadLine();
                    //if (!content.StartsWith(",") && !content.StartsWith(";"))
                    //{
                    //    reader.DiscardBufferedData();
                    //    reader.BaseStream.Seek(0, SeekOrigin.Begin);
                    //    reader.BaseStream.Position = 0;
                    //}
                    if (content.StartsWith(",") || content.StartsWith(";"))
                    {
                        content = await reader.ReadLineAsync();
                    }
                    content = content.ToLower();
                    sb.AppendLine(content);
                    sb.Append(reader.ReadToEnd());
                    var bytes = Encoding.Default.GetBytes(sb.ToString());
                    Stream stream = new MemoryStream(bytes);
                    TextReader treader = new StreamReader(stream);
                    var csvReader = new CsvReader(treader, new CsvHelper.Configuration.CsvConfiguration(new System.Globalization.CultureInfo("zh-CHS")) { BadDataFound = null });
                    csvReader.Context.RegisterClassMap(typeof(EBaySalesDataMap));
                    data = csvReader.GetRecords<EBaySalesData>().ToList();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"读取ebay销售数据文件异常：{ex.Message}");
                RemoveTempFile(sourcefilePath, desPath);
                throw new Exception("eBay销售数据文件异常");
            }
            RemoveTempFile(sourcefilePath, desPath);

            //删除临时文件
            //System.IO.File.Delete(sourcefilePath);
            //if (!string.IsNullOrEmpty(desPath))
            //{
            //    DeleteDir(desPath);
            //}
            return data;
        }

        private Dictionary<string, string> GetDeclareMonth(DateTime startTime, DateTime endTime)
        {
            var result = new Dictionary<string, string>();
            var year = startTime.ToString("yyyy");
            var month = startTime.ToString("MMM", CultureInfo.CreateSpecificCulture("en-GB"));
            var key = year + "-" + month.ToUpper();
            var value = startTime.ToString("yyyy-MM");

            result.Add(key, value);

            if (startTime.ToString("yyyyMM") != endTime.ToString("yyyyMM"))
            {
                var date = startTime;
                do
                {
                    date = date.AddMonths(1);
                    var dateYear = date.ToString("yyyy");
                    var dateMonth = date.ToString("MMM", CultureInfo.CreateSpecificCulture("en-GB"));
                    key = dateYear + "-" + dateMonth.ToUpper();
                    value = date.ToString("yyyy-MM");
                    result.Add(key, value);
                }
                while (!date.ToString("yyyyMM").Equals(endTime.ToString("yyyyMM")));
            }
            return result;
        }

        private void DeleteDir(string file)
        {
            try
            {
                //去除文件夹和子文件的只读属性
                //去除文件夹的只读属性
                System.IO.DirectoryInfo fileInfo = new DirectoryInfo(file);
                fileInfo.Attributes = FileAttributes.Normal & FileAttributes.Directory;

                //去除文件的只读属性
                System.IO.File.SetAttributes(file, System.IO.FileAttributes.Normal);

                //判断文件夹是否还存在
                if (Directory.Exists(file))
                {
                    foreach (string f in Directory.GetFileSystemEntries(file))
                    {
                        if (File.Exists(f))
                        {
                            //如果有子文件删除文件
                            File.Delete(f);
                        }
                        else
                        {
                            //循环递归删除子文件夹
                            DeleteDir(f);
                        }
                    }
                    //删除空文件夹
                    Directory.Delete(file);
                }
            }
            catch (Exception ex) // 异常处理
            {
                //Console.WriteLine(ex.Message.ToString());// 异常信息
                _logger.LogError(ex, $"删除临时文件异常:{ex.Message}");
            }

        }

        /// <summary>
        /// 获取日期范围的所有日期
        /// </summary>
        /// <param name="start"></param>
        /// <param name="end"></param>
        /// <returns></returns>
        public static List<string> GetDateRangeStr(DateTime start, DateTime end)
        {
            List<string> list = new List<string>();
            for (var day = start; day <= end; day = day.AddDays(1))
            {
                list.Add(day.ToString("yyyy-MM-dd"));
            }
            return list;
        }
    }
}

using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Vat.Service.Common.Model;
using Vat.Service.Common.Model.SaleAmountStatistics;
using Vat.Service.Domain.AggregatesModel.TaxAgg;

namespace Vat.Service.Common.Services
{
    public interface IDeclaredCalculateService
    {
        Task<SaleAmountStatisticsResult> AmazonSalesDataCalculate(DateTime startDate, DateTime endDate, string salesFilePath, 
            List<string> existVatCountryCodeList, string rateType, TaxNumber taxNumber);
        Task<Dictionary<string, Money>> VatPayableCalculate(TaxNumber taxNumber,  VatPayableCalculateParam param);
        Task<DeclaredDataModel> ConvertToDeclaredData(TaxNumber taxNumber, VatPayableCalculateParam param, List<DeductionItemModel> deductionItems);
        Task<EBaySaleAmountStatistics> EBaySalesDataCalculate(DateTime startDate, DateTime endDate, string salesFilePath, TaxNumber taxNumber);
        Task<DayAmazonSaleAmountStatistics> AliexpressSalesDataCalculate(DateTime startDate, DateTime endDate, string salesFilePath, TaxNumber taxNumber, List<string> existVatCountryCodeList);
        Task<AliexpressSaleAmountStatistics> EuAliexpressSalesDataCalculate(DateTime startDate, DateTime endDate, string salesFilePath, TaxNumber taxNumber, List<string> existVatCountryCodeList);
        Task<List<AliexpressSalesData>> GetAliexpressSalesData(string aliexpressFilePath);
        /// <summary>
        /// 计算发票金额
        /// </summary>
        /// <param name="invoicefile"></param>
        /// <returns></returns>
        /// <exception cref="VatCommonException"></exception>
        Task<ArabInvoiceData> GetArabInvoiceAmount(ArabInvoiceParam param);
    }
}

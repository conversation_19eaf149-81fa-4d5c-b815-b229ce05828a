using System.Collections.Generic;

namespace Vat.Service.Common.Model
{
    public class EUAmazonSaleAmountStatistics
    {
        public EUAmazonSaleAmountStatistics()
        {
            EUAmazonItems = new List<EUAmazonItem>();
        }
        public List<EUAmazonItem> EUAmazonItems { get; set; }
        /// <summary>
        /// 普通销售总额
        /// </summary>
        public Money AmazonSaleAmount { get; set; }

        /// <summary>
        /// 出口总金额
        /// </summary>
        public Money ExportAmount { get; set; }
        /// <summary>
        /// ZM申报总金额
        /// </summary>
        public Money ZMDeclaredAmount { get; set; }
        /// <summary>
        /// 本土B2B申报总金额
        /// </summary>
        public Money NativeB2BDeclaredAmount { get; set; }
        /// <summary>
        /// "COMMINGLING_BUY"的税金
        /// </summary>
        public Money SellerTaxAmount { get; set; }
        /// <summary>
        ///  出口到欧盟国家的销售额
        /// </summary>
        public Money ExportToEUAmount { get; set; }
        /// <summary>
        ///  出口到非欧盟国家的销售额
        /// </summary>
        public Money ExportToNonEUAmount { get; set; }
        public Money TotalAmazonSaleAmount { get; set; }
    }

    public class EUAmazonItem
    { 
        public string Month { get; set; }
        /// <summary>
        ///本国境内B2B金额
        /// </summary>
        public Money NativeToNativeB2B { get; set; }
        /// <summary>
        /// 欧盟发往本国B2C
        /// </summary>
        public Money EUToNative { get; set; }
        public Money ZmDelcared { get; set; }
        /// <summary>
        /// 出口金额，平台代扣代缴
        /// </summary>
        public Money Export { get; set; }
        /// <summary>
        /// 出口销售金额
        /// </summary>
        public List<EUCurrencyItem> EUCurrencyItems { get; set; }
    }
    public class EUCurrencyItem
    {
        /// <summary>
        /// 销售额金额（本国币种）
        /// 被平台代扣代缴净销售额（平台已经代扣代缴，只申报不交税）
        /// </summary>
        public Money CurrencySalesAmount { get; set; }
       
        /// <summary>
        /// 汇率
        /// </summary>
        public decimal ExchangeRate { get; set; }
        /// <summary>
        /// 销售总额
        /// </summary>
        public Money OrderSalesAmount { get; set; }
        /// <summary>
        /// 跨境B2B
        /// </summary>
        public Money CrossB2BSalesAmount { get; set; }
        /// <summary>
        /// 跨境B2B
        /// </summary>
        public Money NoDutyCrossB2BSalesAmount { get; set; }
        /// <summary>
        /// 本土B2B
        /// </summary>
        public Money NativeB2BSalesAmount { get; set; }
        /// <summary>
        /// 本土-EU B2C
        /// </summary>
        public Money NativeB2CSalesAmount { get; set; }
        /// <summary>
        /// EU-本土 B2B
        /// </summary>
        public Money EuNativeB2BSalesAmount { get; set; }
        /// <summary>
        /// 本土-EU Liquidation 类似清仓或分包
        /// </summary>
        public Money NativeLiqSalesAmount { get; set; }
        public List<LiqInvoiceItem> LiqInvoices { get; set; }
    }
}
using System;
using System.Collections.Generic;
using System.Text;

namespace Vat.Service.Common.Model
{
    /// <summary>
    /// 这个是前端传给我的参数
    /// </summary>
    public class VatPayableCalculateParam
    {
        /// <summary>
        /// 税号
        /// </summary>
        public string TaxNo { get; set; }
        /// <summary>
        /// 税率类型
        /// </summary>
        public string TaxRateType { get; set; }
        public Dictionary<string, decimal> DeclaredItems { get; set; }
        /// <summary>
        /// amazon 申报总额
        /// </summary>
        public decimal AmazonDeclaredSalesAmount { get; set; }
        /// <summary>
        /// amazon 申报总额（含税）
        /// </summary>
        public decimal AmazonDeclaredSalesAmountExclTax { get; set; }
        /// <summary>
        /// amazon 增值税（21年后英国）
        /// </summary>
        public decimal AmazonDeclaredSalesAmountAmtTax { get; set; }


        #region 不分年份的字段
        /// <summary>
        /// 含增值税总额
        /// </summary>
        //public decimal AmazonSaleAmount { get; set; }

        /// <summary>
        /// 增值税
        /// </summary>
        public decimal AmazonAmtTax { get; set; }

        /// <summary>
        /// 其他平台销售税
        /// </summary>
        public decimal OtherSaleTax { get; set; }
        #endregion

        /// <summary>
        ///  其他 申报总额
        /// </summary>
        public decimal OtherDeclaredSalesAmount { get; set; }
        /// <summary>
        ///  其他 申报总额（含税）
        /// </summary>
        public decimal OtherDeclaredSalesAmountInclTax { get; set; }
        /// <summary>
        /// amazon frs税率申报总额
        /// </summary>
        public decimal AmazonDeclaredSalesAmount_OtherRate { get; set; }
        /// <summary>
        /// 其他 frs申报总额
        /// </summary>
        public decimal OtherDeclaredSalesAmount_OtherRate { get; set; }
        /// <summary>
        ///  其他平台 申报总额(21年)
        /// </summary>
        public decimal OtherDeclaredSalesAmountLater { get; set; }
        /// <summary>
        /// EBay不含税销售总额
        /// </summary>
        public decimal EBaySoldForAmount { get; set; }
        /// <summary>
        /// EBay含税销售总额
        /// </summary>
        public decimal EBayTotalPriceAmount { get; set; }
        /// <summary>
        /// EBay代扣代缴总额
        /// </summary>
        public decimal EBaySellerCollectedTaxAmount { get; set; }
        /// <summary>
        /// 出口销售额
        /// </summary>
        public decimal ExportAmount { get; set; }
        /// <summary>
        /// 出口EU销售额
        /// </summary>
        public decimal ExportOutSideAmount { get; set; }
        /// <summary>
        /// 其他平台出口销售额
        /// </summary>
        public decimal OtherExportAmount { get; set; }
        /// <summary>
        /// ZM申报金额
        /// </summary>
        public decimal ZMDeclaredAmount { get; set; }
        /// <summary>
        /// 其他平台ZM申报金额
        /// </summary>
        public decimal OtherZMDeclaredAmount { get; set; }
        /// <summary>
        /// 速卖通销售订单金额
        /// </summary>
        public decimal AliexpressSalesAmount { get; set; }
        /// <summary>
        /// 速卖通出口销售额
        /// </summary>
        public decimal AliexpressExportAmount { get; set; }
        /// <summary>
        /// 速卖通ZM申报金额
        /// </summary>
        public decimal AliexpressZMDeclaredAmount { get; set; }

        /// <summary>
        /// 本土B2B申报金额
        /// </summary>
        public decimal NativeB2BDeclaredAmount { get; set; }
        /// <summary>
        /// 其他本土B2B申报金额
        /// </summary>
        public decimal OtherNativeB2BDeclaredAmount { get; set; }
        /// <summary>
        /// 速卖通本土B2B申报金额
        /// </summary>
        public decimal AliexpressNativeB2BDeclaredAmount { get; set; }
        #region 阿拉伯国家
        /// <summary>
        /// 发票汇总金额
        /// </summary>
        public decimal ExpenseAmount { get; set; } = 0;

        #endregion
        /// <summary>
        /// 加拿大应缴税金
        /// </summary>
        public decimal TaxPayAmount { get; set; } = 0;
        /// <summary>
        /// 墨西哥产品销售金额
        /// </summary>
        public decimal ProductSalesAmount { get; set; } = 0;

        public bool? IsTester { get; set; }

        #region 2025英国亚马逊申报
        /// <summary>
        /// 卖家需要自行缴纳的税金
        /// </summary>
        public decimal? SellerTaxAmount { get; set; }
        /// <summary>
        ///  出口到欧盟国家的销售额
        /// </summary>
        public decimal? ExportToEUAmount { get; set; }
        /// <summary>
        ///  出口到非欧盟国家的销售额
        /// </summary>
        public decimal? ExportToNonEUAmount { get; set; }

        /// <summary>
        /// 未被代扣代缴净销售额
        /// </summary>
        public decimal GBAmazonSaleAmount { get; set; }
        /// <summary>
        /// 卖家需要自行缴纳税金
        /// </summary>
        public decimal GBAmazonSaleTaxAmount { get; set; }
        /// <summary>
        /// 被代扣代缴净销售额
        /// </summary>
        public decimal GBMarketplaceTaxAmount { get; set; }
        /// <summary>
        /// 出口到欧盟国家的销售额
        /// </summary>
        public decimal GBExportToEUAmount { get; set; }
        /// <summary>
        /// 出口到非欧盟国家的销售额
        /// </summary>
        public decimal GBExportToNonEUAmount { get; set; }
        #endregion
    }
}

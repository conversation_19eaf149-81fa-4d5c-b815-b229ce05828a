using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using BFE.Framework.Domain.Core.Paged;
using BFE.Framework.Domain.Core.Specification;
using BFE.Framework.Infrastructure.Authorization.Admin;
using BFE.Framework.Infrastructure.Crosscutting.Adapter;
using BFE.Framework.Infrastructure.Crosscutting.Helpers;
using Microsoft.Extensions.Logging;
using Vat.Service.Application.Common.Helpers;
using Vat.Service.Application.Common.Methods;
using Vat.Service.Application.Common.Model;
using Vat.Service.Application.Common.Model.SaleAmountStatistics;
using Vat.Service.Application.Constants;
using Vat.Service.Application.DataTransferModels.TaxAgent.DerivedClass;
using Vat.Service.Application.DTO.Common;
using Vat.Service.Application.Extensions;
using Vat.Service.Application.Services;
using Vat.Service.Application.ViewModels.Order;
using Vat.Service.Application.ViewModels.Order.Request;
using Vat.Service.Application.ViewModels.Tax;
using Vat.Service.Application.ViewModles.Common;
using Vat.Service.Common.Model;
using Vat.Service.Common.Model.SaleAmountStatistics;
using Vat.Service.Domain.AggregatesModel.OrderAgg;
using Vat.Service.Domain.AggregatesModel.OrderAgg.Specifications;
using Vat.Service.Domain.AggregatesModel.TaxAgg.Specifications;
using Vat.Service.Domain.Constants.Status;
using Vat.Service.Domain.Constants.Type;
using Vat.Service.Domain.Repositories.Order;
using Vat.Service.Domain.Repositories.Tax;
using Vat.Service.Domain.Repositories.VatLogRemark;
using Vat.Service.Infastructure.Exception;
using CommonModel = Vat.Service.Common.Model;

namespace Vat.Service.Application.Queries.Order
{
    public class DeclaredOrderQueries : IDeclaredOrderQueries
    {
        private readonly IDeclaredOrderReadOnlyRepository _declaredOrderReadOnlyRepository;
        private readonly IVatLogRemarkReadOnlyRepository _vatLogRemarkReadOnlyRepository = null;
        private readonly ITypeAdapter _typeAdapter = null;
        private readonly IAdminIdentityService _adminIdentityService = null;
        private readonly ILogger<OrderQueries> _logger = null;
        private readonly IDeductionCodeRepository _deductionCodeRepository;
        private readonly ITaxAgentQueries _taxAgentQueries;
        private readonly ITaxNumberRepository _taxNumberRepository;
        private readonly Vat.Service.Common.Services.IDeclaredCalculateService _declaredCalculateService;
        private readonly FileServiceMethod _fileServiceMethod = null;

        public DeclaredOrderQueries(IDeclaredOrderReadOnlyRepository declaredOrderReadOnlyRepository, IVatLogRemarkReadOnlyRepository vatLogRemarkReadOnlyRepository, ITypeAdapter typeAdapter,
            IAdminIdentityService adminIdentityService, ILogger<OrderQueries> logger, IDeductionCodeRepository deductionCodeRepository, ITaxAgentQueries taxAgentQueries,
            ITaxNumberRepository taxNumberRepository, Vat.Service.Common.Services.IDeclaredCalculateService declaredCalculateService, FileServiceMethod fileServiceMethod)
        {
            _declaredOrderReadOnlyRepository = declaredOrderReadOnlyRepository;
            _vatLogRemarkReadOnlyRepository = vatLogRemarkReadOnlyRepository;
            _typeAdapter = typeAdapter;
            _adminIdentityService = adminIdentityService;
            _logger = logger;
            _deductionCodeRepository = deductionCodeRepository;
            _taxAgentQueries = taxAgentQueries;
            _taxNumberRepository = taxNumberRepository;
            _declaredCalculateService = declaredCalculateService;
            _fileServiceMethod = fileServiceMethod;
        }

        //订单列表
        public async Task<PageResult<DeclaredOrderListModel>> GetDeclaredOrderInPage(DeclaredOrderParam request)
        {
            var result = new PageResult<DeclaredOrderListModel>();
            result.PageNumber = request.PageNumber;
            result.PageSize = request.PageSize;
            result.TotalPages = 0;
            result.TotalRecords = 0;

            ISpecification<DeclaredOrder> specification = GetSpecification(request);

            var sort = new Dictionary<System.Linq.Expressions.Expression<Func<DeclaredOrder, dynamic>>, SortOrder>();
            var sortorder = SortOrder.Descending;
            if (request.SortOrder == "ascending")
            {
                sortorder = SortOrder.Ascending;
            }
            switch (request.SortField)
            {
                case "submitTime":
                    sort.Add(p => p.SubmitTime, sortorder);
                    break;
                case "createTime":
                    sort.Add(p => p.CreateTime, sortorder);
                    break;
                case "acceptedTime":
                    sort.Add(p => p.AcceptedTime, sortorder);
                    break;
                case "finishedTime":
                    sort.Add(p => p.FinishedTime, sortorder);
                    break;
                case "orderId":
                    sort.Add(p => p.OrderId, sortorder);
                    break;
                case "totalAmount":
                    sort.Add(p => p.DeclaredData.TotalSalesAmount.Amount, sortorder);
                    break;
                case "taxDue":
                    sort.Add(p => p.DeclaredData.TaxDue.Amount, sortorder);
                    break;
                default:
                    sort.Add(p => p.CreateTime, sortorder);
                    break;
            }
            //sort.Add(p => p.CreateTime, sortorder);

            var pageResult = await _declaredOrderReadOnlyRepository.FindInPageAsync(request.PageNumber, request.PageSize, specification, sort);

            result.PageNumber = pageResult.PageNumber;
            result.PageSize = pageResult.PageSize;
            result.TotalPages = pageResult.TotalPages;
            result.TotalRecords = pageResult.TotalRecords;

            var dataList = new List<DeclaredOrderListModel>();
            if (pageResult.Data.Count > 0)
            {
                var dataTmp = pageResult.Data;
                dataList = _typeAdapter.Adapt<List<DeclaredOrderListModel>>(dataTmp);
                foreach (var item in dataList)
                {
                    var taxnumber = await _taxNumberRepository.GetAsync(new MatchTaxNumerByIsDeletedSpecification(false)
                     .And(new MatchTaxNumberByTenantSpecification(item.Tenant.Code))
                    .And(new MatchTaxNumberByVatCountrySpecification(item.TaxCountry.Code))
                    .And(new MatchTaxNumberByNoSpecification(item.TaxNumberEntity.GetDeclaredTaxNo())));
                    if (taxnumber == null)
                        continue;
                    if (taxnumber.TaxCountry.Code == "GB")
                    {
                        item.DeclareTaxRateLimit = _typeAdapter.Adapt<DeclareTaxRateLimitModel>(taxnumber.DeclareTaxRateLimit);
                    }
                    item.StoreAuthorizationList = _typeAdapter.Adapt<List<StoreAuthorizationInfo>>(taxnumber.StoreAuthorizationList);
                    item.IsStoreAuthorization = taxnumber.StoreAuthorizationList == null ? false : taxnumber.StoreAuthorizationList.Any();
                    item.DeclareMethod = taxnumber.DeclareMethod;
                    item.DeclaredObject = taxnumber.DeclaredObject;
                    item.TaxItems = taxnumber.TaxItems;
                    item.AgentManagerId = !string.IsNullOrEmpty(taxnumber.AgentManagerId) ? taxnumber.AgentManagerId : "";
                }
            }
            result.Data = dataList;
            return result;
        }

        private ISpecification<DeclaredOrder> GetSpecification(DeclaredOrderParam request, bool isSummary = false)
        {
            ISpecification<DeclaredOrder> specification = new MatchDeclaredOrderByIsDeleteSpecification(false);

            var userInfo = _adminIdentityService.GetAdminUserIdentity();
            var groupsId = userInfo.Groups.ToList();
            ////根据管理组的ID判断是否是超级业务经理，目前超级业务经理组id为：1
            if (groupsId.All(x => x != 1) && groupsId.All(x => x != 112) && groupsId.All(x => x != 113) && groupsId.All(x => x != 114))
            {
                specification = specification.And(new MatchDeclaredOrderByManagerSpecification(userInfo.UserName, ManagerType.SaleManager).Or(new MatchDeclaredOrderByManagerSpecification(userInfo.UserName, ManagerType.CustomerManager)));

            }
            if (!string.IsNullOrEmpty(request.KeyWords))
            {//关键字
                specification = specification.And(new MatchDeclaredOrderByKeyWordsSpecification(request.KeyWords.Trim()));
            }
            if (!string.IsNullOrEmpty(request.VatCountryCode))
            {
                specification = specification.And(new MatchDeclaredOrderByVatCountrySpecification(request.VatCountryCode));
            }
            if (!string.IsNullOrEmpty(request.TenantCode))
            {
                specification = specification.And(new MatchDeclaredOrderByTenantSpecification(request.TenantCode));
            }

            if (!string.IsNullOrEmpty(request.Status))
            {
                specification = specification.And(new MatchDeclaredOrderByStatusSpecification(request.Status));
            }
            if (request.DeclaredStartTime != null && request.DeclaredEndTime != null)
            {//申报开始时间--结束时间
                request.DeclaredEndTime = request.DeclaredEndTime.Value.AddDays(1);
                specification = specification.And(new MatchDeclaredOrderByDeclaredTimeSpecification(request.DeclaredStartTime, request.DeclaredEndTime));
            }
            if (request.CreateStartTime != null && request.CreateEndTime != null)
            {//创建开始时间--结束时间
                request.CreateEndTime = request.CreateEndTime.Value.AddDays(1);
                specification = specification.And(new MatchDeclaredOrderByCreateTimeSpecification(request.CreateStartTime, request.CreateEndTime));
            }
            if (!string.IsNullOrEmpty(request.TaxAgent))
            {//税务代理
                specification = specification.And(new MatchDeclaredOrderByTaxAgentSpecification(request.TaxAgent));
            }
            if (!string.IsNullOrEmpty(request.RegisterMain))
            {//注册主体
                specification = specification.And(new MatchDeclaredOrderByRegisterMainIdSpecification(request.RegisterMain));
            }

            if (request.SaleManager != "all" && request.SaleManager != null)
            {
                specification = specification.And(new MatchDeclaredOrderByManagerSpecification(request.SaleManager, ManagerType.SaleManager));
            }
            if (request.CustomerManager != "all" && request.CustomerManager != null)
            {
                specification = specification.And(new MatchDeclaredOrderByManagerSpecification(request.CustomerManager, ManagerType.CustomerManager));
            }


            if (!string.IsNullOrEmpty(request.DeclaredType))
            {
                specification = specification.And(new MatchDeclaredOrderByDeclaredTypeSpecification(request.DeclaredType));
            }
            if (request.IsTester != null)
            {
                specification = specification.And(new MatchDeclaredOrderByIsTesterSpecification(request.IsTester.Value));
            }
            if (!request.InvoiceStatus.IsNullOrBlank())
            {
                specification = specification.And(new MatchDeclaredOrderByInvoiceStatusSpecification(request.InvoiceStatus));
            }
            if (request.Status == OrderStatus.UnAudit)
            {
                if (request.IsUnAudit)
                {
                    specification = specification.And(new MatchDeclaredOrderByOperationTypeSpecification(OperationType.Audit));
                }
                if (request.IsPaid)
                {
                    specification = specification.And(new MatchDeclaredOrderByOperationTypeSpecification(OperationType.MarkPaid));
                }
                if (request.IsToSubmit)
                {
                    specification = specification.And(new MatchDeclaredOrderByToSubmitSpecification());
                }
            }
            if (!isSummary)
            {
                if (request.TypeOfWorkBench == "WaitDownTaxPayment")
                {
                    specification = specification.And(new MatchDeclaredOrderByToDownloadPaymentVoucherSpecification());
                }
                else if (request.TypeOfWorkBench == "WaitUploadTaxReceipt")
                {
                    specification = specification.And(new MatchDeclaredOrderByToUploadTaxReceiptSpecification().And(new MatchDeclaredOrderByDeclaredTypeSpecification(DeclareBusinessCode.NonZeroDeclaration)));
                }
                else if (request.TypeOfWorkBench == "NotDeclareInTime")
                {
                    specification = specification.And(new MatchDeclaredOrderByStatusSpecification(DeclaredOrderStatus.ToDeclare))
                    .And(new MatchDeclaredOrderByToDeclaredOverTimeSpecification());
                }
                else if (request.TypeOfWorkBench == "NotUploadPaymentInTime")
                {
                    specification = specification.And(new MatchDeclaredOrderByToUploadPaymentSpecification());
                }
                else if (request.TypeOfWorkBench == "ToDeclareByThisMonth")
                {
                    specification = specification.And(new MatchDeclaredOrderByStatusSpecification(DeclaredOrderStatus.ToDeclare))
                    .And(new MatchDeclaredOrderByCreateTimeSpecification(new DateTime(DateTime.Now.Year, DateTime.Now.Month, 1), DateTime.Now));
                }
            }
            return specification;
        }

        private ISpecification<DeclaredOrder> GetSpecificationByWorkBench(DeclaredOrderParam request)
        {
            ISpecification<DeclaredOrder> specification = new MatchDeclaredOrderByIsDeleteSpecification(false);
            var userInfo = _adminIdentityService.GetAdminUserIdentity();
            var groupsId = userInfo.Groups.ToList();
            ////根据管理组的ID判断是否是超级业务经理，目前超级业务经理组id为：1
            if (groupsId.All(x => x != 1) && groupsId.All(x => x != 112) && groupsId.All(x => x != 113) && groupsId.All(x => x != 114))
            {
                specification = specification.And(new MatchDeclaredOrderByManagerSpecification(userInfo.UserName, ManagerType.SaleManager).Or(new MatchDeclaredOrderByManagerSpecification(userInfo.UserName, ManagerType.CustomerManager)));
            }
            if (request.IsTester != null)
            {
                specification = specification.And(new MatchDeclaredOrderByIsTesterSpecification(request.IsTester.Value));
            }
            if (request.TypeOfWorkBench == "WaitDownTaxPayment")
            {
                specification = specification.And(new MatchDeclaredOrderByToDownloadPaymentVoucherSpecification());
            }
            else if (request.TypeOfWorkBench == "WaitUploadTaxReceipt")
            {
                specification = specification.And(new MatchDeclaredOrderByToUploadTaxReceiptSpecification().And(new MatchDeclaredOrderByDeclaredTypeSpecification(DeclareBusinessCode.NonZeroDeclaration)));
            }
            else if (request.TypeOfWorkBench == "NotDeclareInTime")
            {
                specification = specification.And(new MatchDeclaredOrderByStatusSpecification(DeclaredOrderStatus.ToDeclare))
                .And(new MatchDeclaredOrderByToDeclaredOverTimeSpecification());
            }
            else if (request.TypeOfWorkBench == "NotUploadPaymentInTime")
            {
                specification = specification.And(new MatchDeclaredOrderByToUploadPaymentSpecification());
            }
            else if (request.TypeOfWorkBench == "ToDeclareByThisMonth")
            {
                specification = specification.And(new MatchDeclaredOrderByStatusSpecification(DeclaredOrderStatus.ToDeclare))
                .And(new MatchDeclaredOrderByCreateTimeSpecification(new DateTime(DateTime.Now.Year, DateTime.Now.Month, 1), DateTime.Now));
            }
            return specification;
        }
        /// <summary>
        /// 查询订单详情
        /// </summary>
        /// <param name="orderid"></param>
        /// <returns></returns>
        public async Task<DeclaredOrderModel> GetDeclaredOrderByOrderId(string orderid)
        {
            ISpecification<DeclaredOrder> specification = new MatchDeclaredOrderByIsDeleteSpecification(false);
            var userInfo = _adminIdentityService.GetAdminUserIdentity();
            var groupsId = userInfo.Groups;
            specification = specification.And(new MatchDeclaredOrderByOrderIdSpecification(orderid));
            var vatdeclare = await _declaredOrderReadOnlyRepository.GetAsync(specification);
            var result = new DeclaredOrderModel();
            if (vatdeclare == null)
            {
                throw new VatCommonException(Error.Codes.NoFoundDeclaredOrder, Error.Names.NoFoundDeclaredOrder);
            }
            result = _typeAdapter.Adapt<DeclaredOrderModel>(vatdeclare);
            var taxnumber = await _taxNumberRepository.GetAsync(new MatchTaxNumerByIsDeletedSpecification(false)
                  .And(new MatchTaxNumberByNoSpecification(result.TaxNumberEntity.VatNo)));
            if (taxnumber == null)
                return result;
            if (result.TaxCountry.Code == "GB")
            {
                result.DeclaredAmountIsOverLimit = taxnumber.DeclareTaxRateLimit == null ? false : taxnumber.DeclareTaxRateLimit.IsOverLimit;
                result.TaxStartTime = taxnumber.TaxStartTime;
            }
            result.VatProof = taxnumber.RegisterFiles?.VatProof;
            result.AgentManagerId = taxnumber.AgentManagerId;
            result.PayTaxBankAccount = taxnumber.PayTaxBankAccount;
            result.DeclaredObject = taxnumber.DeclaredObject;
            //税代配置
            var agents = await _taxAgentQueries.GetAllTaxAgentSupportBusinessInfo();
            var configs = agents.FirstOrDefault(a => a.TaxAgentId == result.TaxAgentEntity.Code)?.SupportBusinessCountries.FirstOrDefault(c => c.CountryCode == result.TaxCountry.Code)?.SupportBusinessInfos;
            if (configs != null)
            {
                //var taxReceiptConfig = configs.FirstOrDefault(c => c.BusinessCode == "TaxReceipt")?.BusinessConfig;
                //if (taxReceiptConfig != null)
                //{
                //    var config = taxReceiptConfig as PayTaxReceiptBusinessConfig;
                //    result.IsNeedUploadTaxReceipt = new UploadResultConfigModel
                //    {
                //        IsSupportAdminChooseUpload = config.IsSupportAdminChooseUpload,
                //        PortUploadType = config.PortUploadType,
                //        UploadType = config.UploadType
                //    };
                //}
                //var taxPaymentFlowSheetConfig = configs.FirstOrDefault(c => c.BusinessCode == "TaxPaymentFlowSheet")?.BusinessConfig;
                //if (taxPaymentFlowSheetConfig != null)
                //{
                //    var config = taxPaymentFlowSheetConfig as PayTaxPaymentFlowSheetBusinessConfig;
                //    result.IsNeedUploadPaymentVoucher = new UploadResultConfigModel
                //    {
                //        IsSupportAdminChooseUpload = config.IsSupportAdminChooseUpload,
                //        PortUploadType = config.PortUploadType,
                //        UploadType = config.UploadType
                //    };
                //}
                var taxGuidance = configs.FirstOrDefault(c => c.BusinessCode == "TaxGuidance")?.BusinessConfig;
                if (taxGuidance != null)
                {
                    result.IsNeedUploadTaxPaymentReference = true;
                }
            }
            return result;
        }
        /// <summary>
        /// 年报详情
        /// </summary>
        /// <param name="orderid"></param>
        /// <returns></returns>
        public async Task<YearReportOrderModel> GetYearReportOrderByOrderId(string orderid)
        {
            ISpecification<DeclaredOrder> specification = new MatchDeclaredOrderByIsDeleteSpecification(false);
            specification = specification.And(new MatchDeclaredOrderByOrderIdSpecification(orderid));
            var vatdeclare = await _declaredOrderReadOnlyRepository.GetAsync(specification);
            var result = _typeAdapter.Adapt<YearReportOrderModel>(vatdeclare);
            return result;
        }
        public async Task<List<string>> GetHasVatCountries(string orderid)
        {
            ISpecification<DeclaredOrder> specification = new MatchDeclaredOrderByIsDeleteSpecification(false);
            specification = specification.And(new MatchDeclaredOrderByOrderIdSpecification(orderid));
            var vatdeclare = await _declaredOrderReadOnlyRepository.GetAsync(specification);
            if (vatdeclare == null)
                return new List<string>();
            var taxs = await _taxNumberRepository.GetListAsync(new MatchTaxNumerByIsDeletedSpecification(false)
    .And(new MatchTaxNumerByIsAbleToDeclareSpecification(true))
    .And(new MatchTaxNumberByRegisterMainSpecification(vatdeclare.TaxNumberInfo.CompanyId)));
            return taxs == null ? new List<string>() : taxs.Where(t => t.TaxCountry.Code != "IOSS").Select(t => t.TaxCountry.Code).ToList();
        }
        /// <summary>
        /// 统计申报状态数量
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<DeclaredOrderStatusAmountModel> SummarizeStatusAmount(DeclaredOrderParam request)
        {

            ISpecification<DeclaredOrder> specification = GetSpecification(request, true);
            ISpecification<DeclaredOrder> specificationbyWorkBench = GetSpecificationByWorkBench(request);
            ISpecification<DeclaredOrder> todeclareSpecification = null;
            ISpecification<DeclaredOrder> abnormalSpecification = null;
            ISpecification<DeclaredOrder> unacceptSpecification = null;
            ISpecification<DeclaredOrder> acceptingSpecification = null;
            ISpecification<DeclaredOrder> waitCommitTaxAgentSpecification = null;
            //如果走到if里面说明他是工作台传过来的
            if (request.StatusOfWorkBanch == DeclaredOrderStatus.ToDeclare)
            {
                todeclareSpecification = specificationbyWorkBench.And(new MatchDeclaredOrderByStatusSpecification(DeclaredOrderStatus.ToDeclare));
            }
            else
            {
                todeclareSpecification = specification.And(new MatchDeclaredOrderByStatusSpecification(DeclaredOrderStatus.ToDeclare));
            }
            var todeclareCountResult = await _declaredOrderReadOnlyRepository.CountAsync(todeclareSpecification, 2000);

            if (request.StatusOfWorkBanch == DeclaredOrderStatus.Abnormal)
            {
                abnormalSpecification = specificationbyWorkBench.And(new MatchDeclaredOrderByStatusSpecification(DeclaredOrderStatus.Abnormal));
            }
            else
            {
                abnormalSpecification = specification.And(new MatchDeclaredOrderByStatusSpecification(DeclaredOrderStatus.Abnormal));
            }
            var abnormalCountResult = await _declaredOrderReadOnlyRepository.CountAsync(abnormalSpecification, 2000);

            //这里是待审核
            if (request.StatusOfWorkBanch == DeclaredOrderStatus.UnAudit)
            {
                unacceptSpecification = specificationbyWorkBench.And(new MatchDeclaredOrderByStatusSpecification(DeclaredOrderStatus.UnAudit));
            }
            else
            {
                unacceptSpecification = specification.And(new MatchDeclaredOrderByStatusSpecification(DeclaredOrderStatus.UnAudit));
            }
            var unacceptCountResult = await _declaredOrderReadOnlyRepository.CountAsync(unacceptSpecification, 2000);

            if (request.StatusOfWorkBanch == DeclaredOrderStatus.Accepting)
            {
                acceptingSpecification = specificationbyWorkBench.And(new MatchDeclaredOrderByStatusSpecification(DeclaredOrderStatus.Accepting));
            }
            else
            {
                acceptingSpecification = specification.And(new MatchDeclaredOrderByStatusSpecification(DeclaredOrderStatus.Accepting));
            }
            var acceptingCountResult = await _declaredOrderReadOnlyRepository.CountAsync(acceptingSpecification, 2000);

            DeclaredOrderStatusAmountModel result = new DeclaredOrderStatusAmountModel()
            {
                ToDeclare = todeclareCountResult,
                Abnormal = abnormalCountResult,
                Accepting = acceptingCountResult,
                UnAudit = unacceptCountResult
            };

            return result;
        }
        /// <summary>
        /// 查询抵扣项code
        /// </summary>
        /// <param name="orderid"></param>
        /// <returns></returns>
        public async Task<List<DeductionCodeModel>> GetDeductionCodes()
        {
            var result = new List<DeductionCodeModel>();
            var codes = await _deductionCodeRepository.GetAllAsync();
            if (codes == null)
                return result;

            result = _typeAdapter.Adapt<List<DeductionCodeModel>>(codes);
            return result;
        }
        /// <summary>
        /// 申报数据计算
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        public async Task<CommonModel.SaleAmountStatisticsResult> SalesDataCalculate(SalesDataCalculateParam param)
        {
            if (param.TaxRateType == Vat.Service.Domain.Constants.TaxRateType.FRS)
                return new CommonModel.SaleAmountStatisticsResult()
                {
                    RateType = param.TaxRateType,
                    VatCountryCode = param.CountryCode,
                    ExistVatCountryCodeList = param.ExistVatCountryCodeList
                };
            var result = new CommonModel.SaleAmountStatisticsResult();
            try
            {
                result = await AmazonSalesDataCalculate(param);
                if (!param.EBayFilePath.IsNullOrBlank() && param.CountryCode == "GB")
                {
                    var ebayResult = await EBaySalesDataCalculate(param);
                    result.EBaySaleAmountStatistics = ebayResult;
                }
                if (!param.AliexpressFilePath.IsNullOrBlank())
                {
                    if (param.CountryCode == "PL")
                    {
                        var aliexpressResult = await AliexpressSalesDataCalculate(param);
                        result.DayAliexpressSaleAmountStatistics = aliexpressResult;
                    }
                    else
                    {
                        var aliexpressResult = await EuAliexpressSalesDataCalculate(param);
                        result.AliexpressSaleAmountStatistics = aliexpressResult;
                    }
                }
            }
            catch (Exception ex)
            {
                throw new VatCommonException(Error.Codes.AmazonVatSaleamountCalculateError, string.Format(Error.Names.AmazonVatSaleamountCalculateError, ex.Message));
            }
            return result;
        }
        /// <summary>
        /// Amazon申报数据计算
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        public async Task<CommonModel.SaleAmountStatisticsResult> AmazonSalesDataCalculate(SalesDataCalculateParam param)
        {
            if (param.FilePath.IsNullOrBlank())
            {
                return new CommonModel.SaleAmountStatisticsResult()
                {
                    RateType = param.TaxRateType,
                    VatCountryCode = param.CountryCode,
                    ExistVatCountryCodeList = param.ExistVatCountryCodeList
                };
            }
            var taxnumber = await _taxNumberRepository.GetAsync(new MatchTaxNumerByIsDeletedSpecification(false)
                .And(new MatchTaxNumberByNoSpecification(param.TaxNo)));
            if (taxnumber == null)
                throw new VatCommonException(Error.Codes.NoFoundRegisterOrder, string.Format(Error.Names.NoFoundRegisterOrder));
            _logger.LogError($"taxnumber: {taxnumber.TaxCountry.Code},{param.TaxNo}");
            var result = await _declaredCalculateService.AmazonSalesDataCalculate(param.StartDate, param.EndDate, param.FilePath,
               param.ExistVatCountryCodeList, param.TaxRateType, taxnumber);
            return result;
        }
        /// <summary>
        /// Ebay申报数据计算
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        public async Task<CommonModel.EBaySaleAmountStatistics> EBaySalesDataCalculate(SalesDataCalculateParam param)
        {
            var taxnumber = await _taxNumberRepository.GetAsync(new MatchTaxNumerByIsDeletedSpecification(false)
                 .And(new MatchTaxNumberByNoSpecification(param.TaxNo)));
            if (taxnumber == null)
                throw new VatCommonException(Error.Codes.NoFoundRegisterOrder, string.Format(Error.Names.NoFoundRegisterOrder));
            var result = await _declaredCalculateService.EBaySalesDataCalculate(param.StartDate, param.EndDate, param.EBayFilePath, taxnumber);
            return result;
        }
        /// <summary>
        /// Aliexpress申报数据计算
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        public async Task<CommonModel.DayAmazonSaleAmountStatistics> AliexpressSalesDataCalculate(SalesDataCalculateParam param)
        {
            var taxnumber = await _taxNumberRepository.GetAsync(new MatchTaxNumerByIsDeletedSpecification(false)
                 .And(new MatchTaxNumberByNoSpecification(param.TaxNo)));
            if (taxnumber == null)
                throw new VatCommonException(Error.Codes.NoFoundRegisterOrder, string.Format(Error.Names.NoFoundRegisterOrder));
            var result = await _declaredCalculateService.AliexpressSalesDataCalculate(param.StartDate, param.EndDate, param.AliexpressFilePath, taxnumber, param.ExistVatCountryCodeList);
            return result;
        }
        /// <summary>
        /// Aliexpress申报数据计算(欧盟国家)
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        public async Task<CommonModel.AliexpressSaleAmountStatistics> EuAliexpressSalesDataCalculate(SalesDataCalculateParam param)
        {
            var taxnumber = await _taxNumberRepository.GetAsync(new MatchTaxNumerByIsDeletedSpecification(false)
                 .And(new MatchTaxNumberByNoSpecification(param.TaxNo)));
            if (taxnumber == null)
                throw new VatCommonException(Error.Codes.NoFoundRegisterOrder, string.Format(Error.Names.NoFoundRegisterOrder));
            var result = await _declaredCalculateService.EuAliexpressSalesDataCalculate(param.StartDate, param.EndDate, param.AliexpressFilePath, taxnumber, param.ExistVatCountryCodeList);
            return result;
        }
        /// <summary>
        /// 应缴税额数据计算
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        public async Task<Dictionary<string, CommonModel.Money>> VatDueCalculate(CommonModel.VatPayableCalculateParam param)
        {
            var taxnumber = await _taxNumberRepository.GetAsync(new MatchTaxNumerByIsDeletedSpecification(false)
                 .And(new MatchTaxNumberByNoSpecification(param.TaxNo)));
            if (taxnumber == null)
                throw new VatCommonException(Error.Codes.NoFoundRegisterOrder, string.Format(Error.Names.NoFoundRegisterOrder));
            var result = await _declaredCalculateService.VatPayableCalculate(taxnumber, param);
            return result;
        }
        /// <summary>
        /// 查询税号未完成的待申报
        /// </summary>
        /// <param name="vatno"></param>
        /// <returns></returns>
        public async Task<List<UnFinishedDeclare>> GetUnFinishedDeclared(string vatno)
        {
            List<UnFinishedDeclare> result = new List<UnFinishedDeclare>();
            List<string> status = new List<string> { DeclaredOrderStatus.Finished, DeclaredOrderStatus.Canceled };
            var orderlist = await _declaredOrderReadOnlyRepository.GetListAsync(new MatchDeclaredOrderByIsDeleteSpecification(false)
                .And(new MatchDeclaredOrderByTaxNoSpecification(vatno)));
            result = orderlist.Where(o => !status.Contains(o.Status))
                .Select(o => new UnFinishedDeclare() { Status = o.Status, DeclarationCycle = o.DeclarationCycle.ToDTO() }).ToList();
            return result;
        }

        /// <summary>
        /// 申报文件包
        /// </summary>
        /// <param name="oid"></param>
        /// <returns></returns>
        public async Task<Stream> GetDeclaredDataPackageByOrderId(List<string> oid)
        {
            //查询税号id
            if (!oid.Any())
                throw new VatCommonException(Error.Codes.NoFoundDeclaredOrder, Error.Names.NoFoundDeclaredOrder);
            ISpecification<DeclaredOrder> specification = new MatchDeclaredOrderByIsDeleteSpecification(false);
            var userInfo = _adminIdentityService.GetAdminUserIdentity();
            var groupsId = userInfo.Groups.ToList();
            ////根据管理组的ID判断是否是超级业务经理，目前超级业务经理组id为：1
            if (groupsId.All(x => x != 1) && groupsId.All(x => x != 112) && groupsId.All(x => x != 113) && groupsId.All(x => x != 114))
            {
                specification = specification.And(new MatchDeclaredOrderByManagerSpecification(userInfo.UserName, ManagerType.SaleManager).Or(new MatchDeclaredOrderByManagerSpecification(userInfo.UserName, ManagerType.CustomerManager)));
            }
            specification = specification.And(new MatchDeclaredOrderByOrderIdsSpecification(oid));
            var query = await _declaredOrderReadOnlyRepository.GetListAsync(specification);

            var paraList = new List<string>();
            Dictionary<string, List<string>> fileNameDic = new Dictionary<string, List<string>>();
            var euCountryDic = ConstantHelper.GetConstantInfoList(typeof(CommonModel.EUCountryInfo)).ToDictionary(c => c.Value, c => c.Description);
            euCountryDic.Add("GB", "英国");

            CustomerFile cf = new CustomerFile()
            {
                RequestTenantCode = "Admin"
            };
            foreach (var declare in query)
            {
                //如果发票处理中或失败，不导出资料文件
                //if (new List<string> { InvoiceStatus.Processing, InvoiceStatus.Failed }.Contains(declare.InvoiceStatus))
                //    continue;
                if (declare.DeclaredFiles == null)
                    continue;
                var fileNameList = new List<string>();
                if (!string.IsNullOrEmpty(declare.DeclaredFiles.AmazonSalesDocument))
                {
                    paraList.Add(declare.DeclaredFiles.AmazonSalesDocument);
                    fileNameList.Add(Path.GetFileName(declare.DeclaredFiles.AmazonSalesDocument));
                }
                if (!string.IsNullOrEmpty(declare.DeclaredFiles.DeclaredConfirmFile))
                {
                    paraList.Add(declare.DeclaredFiles.DeclaredConfirmFile);
                    fileNameList.Add(Path.GetFileName(declare.DeclaredFiles.DeclaredConfirmFile));
                }
                if (!string.IsNullOrEmpty(declare.DeclaredFiles.DeclaredConfirmFileBySytem))
                {
                    paraList.Add(declare.DeclaredFiles.DeclaredConfirmFileBySytem);
                    fileNameList.Add(Path.GetFileName(declare.DeclaredFiles.DeclaredConfirmFileBySytem));
                }
                if (!string.IsNullOrEmpty(declare.DeclaredFiles.DeclaredReceipt))
                {
                    paraList.Add(declare.DeclaredFiles.DeclaredReceipt);
                    fileNameList.Add(Path.GetFileName(declare.DeclaredFiles.DeclaredReceipt));
                }
                if (!string.IsNullOrEmpty(declare.DeclaredFiles.OtherSalesDocument))
                {
                    paraList.Add(declare.DeclaredFiles.OtherSalesDocument);
                    fileNameList.Add(Path.GetFileName(declare.DeclaredFiles.OtherSalesDocument));
                }
                if (!string.IsNullOrEmpty(declare.DeclaredFiles.AliexpressSalesDocument))
                {
                    paraList.Add(declare.DeclaredFiles.AliexpressSalesDocument);
                    fileNameList.Add(Path.GetFileName(declare.DeclaredFiles.AliexpressSalesDocument));
                }
                if (!string.IsNullOrEmpty(declare.DeclaredFiles.EBaySalesDocument))
                {
                    paraList.Add(declare.DeclaredFiles.EBaySalesDocument);
                    fileNameList.Add(Path.GetFileName(declare.DeclaredFiles.EBaySalesDocument));
                }
                if (declare.DeclaredFiles.OtherDataFiles != null && declare.DeclaredFiles.OtherDataFiles.Any())
                {
                    declare.DeclaredFiles.OtherDataFiles.ForEach(item =>
                    {
                        paraList.Add(item.Path);
                        fileNameList.Add(Path.GetFileName(item.Path));
                    });

                }
                if (declare.DeclaredFiles.SummaryReport != null && declare.DeclaredFiles.SummaryReport.Any())
                {
                    declare.DeclaredFiles.SummaryReport.ForEach(item =>
                    {
                        paraList.Add(item);
                        fileNameList.Add(Path.GetFileName(item));
                    });

                }
                if (!string.IsNullOrEmpty(declare.DeclaredFiles.TaxPaymentFile))
                {
                    paraList.Add(declare.DeclaredFiles.TaxPaymentFile);
                    fileNameList.Add(Path.GetFileName(declare.DeclaredFiles.TaxPaymentFile));
                }
                if (!string.IsNullOrEmpty(declare.DeclaredFiles.TaxPaymentGuidance))
                {
                    paraList.Add(declare.DeclaredFiles.TaxPaymentGuidance);
                    fileNameList.Add(Path.GetFileName(declare.DeclaredFiles.TaxPaymentGuidance));
                }
                if (!string.IsNullOrEmpty(declare.DeclaredFiles.TaxPaymentReceipt))
                {
                    paraList.Add(declare.DeclaredFiles.TaxPaymentReceipt);
                    fileNameList.Add(Path.GetFileName(declare.DeclaredFiles.TaxPaymentReceipt));
                }
                if (!string.IsNullOrEmpty(declare.DeclaredFiles.Invoice))
                {
                    paraList.Add(declare.DeclaredFiles.Invoice);
                    fileNameList.Add(Path.GetFileName(declare.DeclaredFiles.Invoice));
                }
                if (!string.IsNullOrEmpty(declare.DeclaredFiles.UEOrderFile))
                {
                    paraList.Add(declare.DeclaredFiles.UEOrderFile);
                    fileNameList.Add(Path.GetFileName(declare.DeclaredFiles.UEOrderFile));
                }
                //抵扣项
                if (declare.DeclaredData?.DeductionItems != null && declare.DeclaredData.DeductionItems.Any())
                {
                    declare.DeclaredData.DeductionItems.ForEach(item =>
                    {
                        if (item != null && item.File.Any())
                        {
                            item.File.ForEach(f =>
                            {
                                paraList.Add(f);
                                fileNameList.Add(Path.GetFileName(f));
                            });
                        }
                    });
                }

                if (declare.YearReportItems != null && declare.YearReportItems.Any())
                {
                    declare.YearReportItems.ForEach(item =>
                    {
                        if (!item.File.IsNullOrBlank())
                        {
                            paraList.Add(item.File);
                            fileNameList.Add(Path.GetFileName(item.File));
                        }
                    });
                }

                var tax = await _taxNumberRepository.GetAsync(new MatchTaxNumerByIsDeletedSpecification(false)
            .And(new MatchTaxNumberByTenantCodeSpecification(declare.Tenant.Code))
          .And(new MatchTaxNumberByNoSpecification(declare.TaxNumberInfo.TaxNumberEntity.VatNo)));
                string agentManagerId = tax == null ? "" : tax.AgentManagerId;
                string key = $"{agentManagerId}_{declare.TaxNumberInfo.CnName}_{declare.DeclarationCycle.StartTime.Value.ToString("yyyy-MM-dd")}--{declare.DeclarationCycle.EndTime.Value.ToString("yyyy-MM-dd")}";
                string newkey = FileHelper.GetListIncreaseName(fileNameDic.Keys.ToList(), key);
                fileNameDic.Add(newkey, fileNameList);
            }
            if (!paraList.Any())
            {
                //return new MemoryStream();
                throw new VatCommonException(Error.Codes.NotFiles, Error.Names.NotFiles);
            }
            foreach (var variable in paraList)
            {
                cf.RequestFilePath.Add(variable?.Substring(variable.LastIndexOf('/') + 1));
            }
            var para = Newtonsoft.Json.JsonConvert.SerializeObject(cf);
            var ms = _fileServiceMethod.GetFileServerMemoryStream(para, fileNameDic);

            return ms;
        }
        public async Task<ArabInvoiceData> GetInvoiceAmount(ArabInvoiceParam param)
        {
            var result = await _declaredCalculateService.GetArabInvoiceAmount(param);
            return result;
        }

        public async Task<Stream> ExportOrders(DeclaredOrderParam request)
        {
            var userInfo = _adminIdentityService.GetAdminUserIdentity();
            var groupsId = userInfo.Groups;
            var isSuperManager = false;
            ////根据管理组的ID判断是否是超级业务经理，目前超级业务经理组id为：1
            if (groupsId.Any(x => x == 1))
            {
                isSuperManager = true;
            }
            ISpecification<DeclaredOrder> specification = GetSpecification(request);
            if (request.CheckedOrderIds != null && request.CheckedOrderIds.Any())
            {
                specification = new MatchDeclaredOrderByOrderIdsSpecification(request.CheckedOrderIds);
            }
            var sort = new Dictionary<System.Linq.Expressions.Expression<Func<DeclaredOrder, dynamic>>, SortOrder>();
            sort.Add(p => p.CreateTime, SortOrder.Descending);

            var declaredOrders = await _declaredOrderReadOnlyRepository.GetListAsync(specification, sort);

            var stream = new MemoryStream();
            var path = Directory.GetCurrentDirectory();
            var template = Path.Combine(path, "StaticFiles", "vatorder.xlsx");
            var excel = new NPOIExcel();
            excel.Open(template);
            excel.ActiveSheet(0);
            excel.SetCurrentSheet(0);

            if (!declaredOrders.Any())
            {
                excel.Save(stream);
                return stream;
            }
            var declareTypeDic = ConstantHelper.GetConstantInfoList(typeof(DeclareType)).ToDictionary(c => c.Value, c => c.Description);
            var orderStatusDic = ConstantHelper.GetConstantInfoList(typeof(DeclaredOrderStatus)).ToDictionary(c => c.Value, c => c.Description);

            for (int i = 0; i < declaredOrders.Count(); i++)
            {
                WriteVatRegister(excel, i + 1, declaredOrders.ElementAt(i), declareTypeDic, orderStatusDic, isSuperManager);
            }
            excel.Save(stream);

            return stream;

        }

        void WriteVatRegister(NPOIExcel excel, int rowIndex,
           DeclaredOrder declareOrder, Dictionary<string, string> declareType, Dictionary<string, string> orderstatus, bool isSuperManager)
        {
            var taxnumber = _taxNumberRepository.GetAsync(new MatchTaxNumberByTenantSpecification(declareOrder.Tenant.Code)
                    .And(new MatchTaxNumberByVatCountrySpecification(declareOrder.TaxNumberInfo.TaxCountry.Code))
                    .And(new MatchTaxNumberByNoSpecification(declareOrder.TaxNumberInfo.TaxNumberEntity.VatNo))).GetAwaiter().GetResult();

            excel.CreateRow(rowIndex);
            excel.CreateCell(0);
            excel.WriteCell(declareOrder.OrderId);

            excel.CreateCell(1);
            var taxnumberentity = declareOrder.TaxNumberInfo.TaxNumberEntity;
            //if (declareOrder.TaxNumberInfo.TaxCountry.Code == "DE")
            //{
            //    taxno = string.Concat(taxno, " \n", $"本地税号：{taxnumberentity.RefNo}");
            //}
            //else if (declareOrder.TaxNumberInfo.TaxCountry.Code == "FR")
            //{
            //    taxno = string.Concat(taxno, " \n", $"SIRET号：{taxnumberentity.SIRET}");
            //}
            excel.WriteCell(taxnumberentity.VatNo);

            excel.CreateCell(2);
            excel.WriteCell(taxnumberentity.RefNo);

            excel.CreateCell(3);
            excel.WriteCell(taxnumberentity.SIRET);

            excel.CreateCell(4);
            excel.WriteCell(declareOrder.TaxNumberInfo.CnName);
            excel.CreateCell(5);
            excel.WriteCell(declareOrder.TaxNumberInfo.EnName);
            excel.CreateCell(6);
            excel.WriteCell(declareOrder.TaxNumberInfo.TaxCountry.CnName);
            excel.CreateCell(7);
            //excel.WriteCell(declareOrder.Tenant.Name);
            excel.WriteCell(declareOrder.Tenant.Name.IsNullOrBlank() ? "-" : declareOrder.Tenant?.Name);
            excel.CreateCell(8);
            excel.WriteCell(declareOrder.Tenant.RegisterPhoneNo.IsNullOrBlank() ? "-" : declareOrder.Tenant.RegisterPhoneNo);
            excel.CreateCell(9);
            excel.WriteCell(declareOrder.Tenant.Code);

            excel.CreateCell(10);
            excel.WriteCell($"{declareOrder.DeclarationCycle.StartTime.Value.ToString("yyyy-MM-dd")}~{declareOrder.DeclarationCycle.EndTime.Value.ToString("yyyy-MM-dd")}");

            //含税
            excel.CreateCell(11);
            excel.WriteCell(declareOrder.DeclaredData?.DeclareSaleAmountInclTax?.Amount);

            //不含税
            excel.CreateCell(12);
            excel.WriteCell(declareOrder.DeclaredData?.DeclareSaleAmountExclTax?.Amount);

            //申报销售总额
            excel.CreateCell(13);
            excel.WriteCell(declareOrder.DeclaredData?.TotalSalesAmount?.Amount);

            //销售税
            excel.CreateCell(14);
            excel.WriteCell(declareOrder.DeclaredData?.SalesVatAmount?.Amount);
            //进口增值税
            excel.CreateCell(15);
            excel.WriteCell(declareOrder.DeclaredData?.ImportVatTaxAmount?.Amount);

            //其他抵扣
            excel.CreateCell(16);
            excel.WriteCell(declareOrder.DeclaredData?.DeductionAmount?.Amount);

            //应缴税费
            excel.CreateCell(17);
            excel.WriteCell(declareOrder.DeclaredData?.TaxDue?.Amount);


            //申报类型
            excel.CreateCell(18);
            excel.WriteCell(declareType.ContainsKey(declareOrder.DeclarationType) ? declareType[declareOrder.DeclarationType] : "");
            //申报税率
            excel.CreateCell(19);
            excel.WriteCell(declareOrder.TaxRateType == Domain.Constants.TaxRateType.FRS ? "FRS税率" : $"普通税率 {Math.Round(declareOrder.TaxRate, 1)}%");

            //税务代理
            excel.CreateCell(20);
            excel.WriteCell(isSuperManager ? declareOrder.TaxAgentEntity.Name : "");

            //税代管理税号ID

            excel.CreateCell(21);
            excel.WriteCell(taxnumber == null ? "" : string.IsNullOrEmpty(taxnumber.AgentManagerId) ? "" : taxnumber.AgentManagerId);

            //销售经理
            excel.CreateCell(22);
            excel.WriteCell(declareOrder.SaleClientManager?.Name);

            //客服经理
            excel.CreateCell(23);
            excel.WriteCell(declareOrder.CustomerServiceManager?.Name);

            //状态
            excel.CreateCell(24);
            excel.WriteCell(orderstatus[declareOrder.Status]);

            //生成日期
            excel.CreateCell(25);
            excel.WriteCell(declareOrder.CreateTime.ToString("yyyy-MM-dd"));


            //提交时间
            excel.CreateCell(26);
            excel.WriteCell(declareOrder.SubmitTime?.ToString("yyyy-MM-dd"));

            //受理时间
            excel.CreateCell(27);
            excel.WriteCell(declareOrder.AcceptedTime?.ToString("yyyy-MM-dd"));
            //完成时间
            excel.CreateCell(28);
            excel.WriteCell(declareOrder.FinishedTime?.ToString("yyyy-MM-dd"));

        }

        public async Task<List<BatchUploadDeclaredReceiptList>> GetUploadDeclaredReceiptList(List<string> ids)
        {
            var list = new List<BatchUploadDeclaredReceiptList>();
            foreach (var id in ids)
            {
                var item = await _declaredOrderReadOnlyRepository.GetByKeyAsync(id);
                list.Add(new BatchUploadDeclaredReceiptList() { DeclaredCycle = $"{item.DeclarationCycle.StartTime.Value.ToString("yyyy-MM-dd")}~{item.DeclarationCycle.EndTime.Value.ToString("yyyy-MM-dd")}", VatNo = item.TaxNumberInfo.TaxNumberEntity.VatNo });
            }
            return list;
        }
    }
}

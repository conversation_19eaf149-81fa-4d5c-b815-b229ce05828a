using System;
using System.Threading;
using System.Threading.Tasks;
using BFE.Framework.Domain.Core;
using BFE.Framework.Infrastructure.Authorization.Tenant;
using BFE.Framework.Infrastructure.Crosscutting.Adapter;
using MediatR;
using Vat.Service.Application.Commands.Order;
using Vat.Service.Application.Common.Model;
using Vat.Service.Domain.AggregatesModel.OrderAgg.Specifications;
using Vat.Service.Domain.Repositories;
using Vat.Service.Infastructure.Exception;
using Vat.Service.Domain.Repositories.Order;
using Vat.Service.Application.Services;
using Microsoft.Extensions.Logging;
using Vat.Service.Application.ViewModels.VatLog;
using Vat.Service.Domain.Constants.Type;
using Vat.Service.Application.ViewModels.Order.Request;
using Vat.Service.Domain.Repositories.Tax;
using Vat.Service.Domain.AggregatesModel.TaxAgg.Specifications;
using BFE.Framework.Infrastructure.Authorization.Admin;
using System.Collections.Generic;
using System.Linq;
using Vat.Service.Domain.Constants.Status;
using Newtonsoft.Json;
using Vat.Service.Application.Extensions;
using Vat.Service.Domain.Constants;
using Vat.Service.Application.Common.Helpers;
using BFE.Framework.Infrastructure.Crosscutting.Helpers;
using CommonModel = Vat.Service.Common.Model;
using Vat.Service.Application.Queries;

namespace Vat.Service.Application.Commands.Tax.Handlers
{
    public class BatchSubmitDeclaredOrderCommandHandler : IRequestHandler<BatchSubmitDeclaredOrderCommand, bool>
    {
        private readonly IDeclaredOrderRepository _declaredOrderRepository;
        private readonly IDBContext _dbContext;
        private readonly IMediator _mediator;
        private readonly IAdminIdentityService _adminIdentityService;
        private readonly ITypeAdapter _typeAdapter;
        private readonly IOperatorLogService _operatorLogService;
        private readonly ILogger<BatchSubmitDeclaredOrderCommandHandler> _logger;
        private readonly Vat.Service.Common.Services.IDeclaredCalculateService _declaredCalculateService;
        private readonly ITaxNumberRepository _taxNumberRepository;
        private readonly IDeclaredOrderQueries _declaredOrderQueries;

        public BatchSubmitDeclaredOrderCommandHandler(IDeclaredOrderRepository declaredOrderRepository, IDBContext dbContext, IMediator mediator,
            IAdminIdentityService adminIdentityService, ITypeAdapter typeAdapter, IOperatorLogService operatorLogService, ILogger<BatchSubmitDeclaredOrderCommandHandler> logger,
            Vat.Service.Common.Services.IDeclaredCalculateService declaredCalculateService, ITaxNumberRepository taxNumberRepository, IDeclaredOrderQueries declaredOrderQueries)
        {
            _declaredOrderRepository = declaredOrderRepository;
            _dbContext = dbContext;
            _mediator = mediator;
            _adminIdentityService = adminIdentityService;
            _typeAdapter = typeAdapter;
            _operatorLogService = operatorLogService;
            _logger = logger;
            _declaredCalculateService = declaredCalculateService;
            _taxNumberRepository = taxNumberRepository;
            _declaredOrderQueries = declaredOrderQueries;
        }

        public async Task<bool> Handle(BatchSubmitDeclaredOrderCommand request, CancellationToken cancellationToken)
        {
            if (request.DeclaredOrderItems == null || !request.DeclaredOrderItems.Any())
            {
                return false;
            }
            List<string> errMsgs = new List<string>();
            foreach (var item in request.DeclaredOrderItems)
            {
                try
                {
                    //提交申报记录
                    var order = await _declaredOrderRepository.GetAsync(new MatchDeclaredOrderByOrderIdSpecification(item.OrderId));
                    if (order == null || order.Status != DeclaredOrderStatus.ToDeclare)
                    {
                        continue;
                    }
                    var files = _typeAdapter.Adapt<Vat.Service.Domain.Common.Models.DeclaredFile>(item.DeclaredFile);
                    var data = new Vat.Service.Domain.Common.Models.DeclaredData();
                    List<string> autoClalCountry = new List<string> { "GB", Constants.EUCountryInfo.FR, Constants.EUCountryInfo.IT, Constants.EUCountryInfo.ES,
                Constants.EUCountryInfo.DE,Constants.EUCountryInfo.AT,Constants.EUCountryInfo.PL,Constants.EUCountryInfo.CZ,Constants.EUCountryInfo.SE,
                        Constants.EUCountryInfo.NL,Constants.ArabCountry.SA,Constants.ArabCountry.AE };
                    //申报计算结果存储
                    var taxnumber = await _taxNumberRepository.GetAsync(new MatchTaxNumerByIsDeletedSpecification(false)
                       .And(new MatchTaxNumberByTenantCodeSpecification(order.Tenant.Code))
                       .And(new MatchTaxNumberByNoSpecification(order.TaxNumberInfo.GetDeclaredTaxNo())));
                    var calcdata = new CommonModel.SaleAmountStatisticsResult();
                    decimal amazonDeclaredSalesAmount = 0;
                    decimal amazonDeclaredSalesAmount_OtherRate = 0;
                    decimal exportamount = 0;
                    decimal zmdeclare = 0;
                    decimal aeexport = 0;
                    decimal aezmdeclre = 0;
                    decimal aesaleamount = 0;
                    decimal amazonAmtTax = 0;
                    decimal nativeb2b = 0;
                    decimal aenativeb2b = 0;
                    string currencycode = "EUR";
                    // 卖家需要自行缴纳的税金
                    decimal sellerTaxAmount = 0;
                    // 出口到欧盟国家的销售额
                    decimal exportToEUAmount = 0;
                    // 出口到非欧盟国家的销售额
                    decimal exportToNonEUAmount = 0;
                    // Amazon申报销售总额值
                    decimal autoCalculateDeclareSaleAmount = 0;
                    // 是否英国亚马逊申报
                    bool isGB = false;
                    if (item.DeclaredType == DeclareType.NonZeroDeclaration && autoClalCountry.Contains(order.TaxNumberInfo.TaxCountry.Code))
                    {
                        //amazon文件计算
                        //calcdata = await _declaredCalculateService.AmazonSalesDataCalculate(order.DeclarationCycle.StartTime.Value.ToLocalTime(), order.DeclarationCycle.EndTime.Value.ToLocalTime(),
                        //  item.DeclaredFile?.AmazonSalesDocument, item.ExistsVatNoCountry, item.TaxRateType, taxnumber);
                        calcdata = await _declaredOrderQueries.SalesDataCalculate(new SalesDataCalculateParam
                        {
                            AliexpressFilePath = item.DeclaredFile.AliexpressSalesDocument,
                            CountryCode = order.TaxNumberInfo.TaxCountry.Code,
                            EBayFilePath = item.DeclaredFile.EBaySalesDocument,
                            ExistVatCountryCodeList = item.ExistsVatNoCountry,
                            EndDate = order.DeclarationCycle.EndTime.Value.ToLocalTime(),
                            FilePath = item.DeclaredFile?.AmazonSalesDocument,
                            StartDate = order.DeclarationCycle.StartTime.Value.ToLocalTime(),
                            TaxNo = order.TaxNumberInfo.GetDeclaredTaxNo(),
                            TaxRateType = item.TaxRateType,
                        });

                        if (calcdata.AmazonSaleCalculateModel == null)
                        {
                            amazonDeclaredSalesAmount = calcdata.NormalStatisticsInfo == null ? 0 : calcdata.NormalStatisticsInfo.NormalSaleAmount.Amount;
                            currencycode = calcdata.NormalStatisticsInfo == null ? "GBP" : calcdata.NormalStatisticsInfo.NormalSaleAmount.CurrencyCode;
                        }
                        else if (calcdata.AmazonSaleCalculateModel.TotalSaleAmount != null)
                        {
                            amazonDeclaredSalesAmount = calcdata.AmazonSaleCalculateModel.TotalSaleAmount.Amount;
                            currencycode = calcdata.AmazonSaleCalculateModel.TotalSaleAmount.CurrencyCode;
                        }
                        if (calcdata.GBAmazonSaleAmountStatistics != null && calcdata.GBAmazonSaleAmountStatistics.NormalSaleAmount != null)
                        {
                            amazonDeclaredSalesAmount = calcdata.GBAmazonSaleAmountStatistics.NormalSaleAmount.Amount;
                            amazonAmtTax = calcdata.GBAmazonSaleAmountStatistics.NormalSaleAmountAmtTax.Amount;
                            currencycode = calcdata.GBAmazonSaleAmountStatistics.NormalSaleAmount.CurrencyCode;
                        }
                        if (calcdata.EUAmazonSaleAmountStatistics != null && calcdata.EUAmazonSaleAmountStatistics.AmazonSaleAmount != null)
                        {
                            amazonDeclaredSalesAmount = calcdata.EUAmazonSaleAmountStatistics.AmazonSaleAmount.Amount;
                            currencycode = calcdata.EUAmazonSaleAmountStatistics.AmazonSaleAmount.CurrencyCode;
                            zmdeclare = calcdata.EUAmazonSaleAmountStatistics.ZMDeclaredAmount == null ? 0 : calcdata.EUAmazonSaleAmountStatistics.ZMDeclaredAmount.Amount;
                            exportamount = calcdata.EUAmazonSaleAmountStatistics.ExportAmount.Amount;
                            nativeb2b = calcdata.EUAmazonSaleAmountStatistics.NativeB2BDeclaredAmount != null ? calcdata.EUAmazonSaleAmountStatistics.NativeB2BDeclaredAmount.Amount : 0;
                        }
                        if (order.TaxNumberInfo.TaxCountry.Code == "GB" && calcdata.EUAmazonSaleAmountStatistics != null)
                        {
                            amazonDeclaredSalesAmount = calcdata.EUAmazonSaleAmountStatistics.ExportAmount.Amount;
                            exportamount = calcdata.EUAmazonSaleAmountStatistics.ExportAmount.Amount;
                            amazonAmtTax = calcdata.EUAmazonSaleAmountStatistics.AmazonSaleAmount.Amount;
                            if (calcdata.EUAmazonSaleAmountStatistics.SellerTaxAmount != null)
                            {
                                sellerTaxAmount = calcdata.EUAmazonSaleAmountStatistics.SellerTaxAmount.Amount;
                                exportToEUAmount = calcdata.EUAmazonSaleAmountStatistics.ExportToEUAmount.Amount;
                                exportToNonEUAmount = calcdata.EUAmazonSaleAmountStatistics.ExportToNonEUAmount.Amount;
                            }
                            if (order.IsTester == true)
                            {
                                isGB = true;
                                // amazonAmtTax + ebaySellerCollectedTaxAmount + otherSaleTax + gbpva
                                var dicDeduction = new Dictionary<string, decimal>();
                                var deductionItems = item.DeclaredItems.Where(d => !d.Code.IsNullOrBlank()).ToList();
                                foreach (var dItem in deductionItems)
                                {
                                    dicDeduction.Add(dItem.Code, dItem.Amount.Amount);
                                }
                                decimal gbpva = 0;
                                if (dicDeduction.ContainsKey("D0007"))//PVA递延
                                    gbpva = Math.Round(dicDeduction["D0007"], 2);
                                autoCalculateDeclareSaleAmount = Math.Round((amazonAmtTax / 1.2M) * 0.2M + item.EBaySellerCollectedTaxAmount + item.OtherSaleTax + gbpva, 2);
                            }
                        }
                        if (calcdata.OtherStatisticsInfo != null && calcdata.OtherStatisticsInfo.OtherSaleAmount != null)
                        {
                            amazonDeclaredSalesAmount_OtherRate = calcdata.OtherStatisticsInfo.OtherSaleAmount.Amount;
                        }
                        if (calcdata.AliexpressSaleAmountStatistics != null && calcdata.AliexpressSaleAmountStatistics.AmazonSaleAmount != null)
                        {
                            aesaleamount = calcdata.AliexpressSaleAmountStatistics.AmazonSaleAmount.Amount;
                            currencycode = calcdata.AliexpressSaleAmountStatistics.AmazonSaleAmount.CurrencyCode;
                            aeexport = calcdata.AliexpressSaleAmountStatistics.ExportAmount.Amount;
                            aezmdeclre = calcdata.AliexpressSaleAmountStatistics.ZMDeclaredAmount.Amount;
                            aenativeb2b = calcdata.AliexpressSaleAmountStatistics.NativeB2BDeclaredAmount != null ? calcdata.AliexpressSaleAmountStatistics.NativeB2BDeclaredAmount.Amount : 0;
                        }
                        if (calcdata.DayAliexpressSaleAmountStatistics != null && calcdata.DayAliexpressSaleAmountStatistics.AmazonSaleAmount != null)
                        {
                            aesaleamount = calcdata.DayAliexpressSaleAmountStatistics.AmazonSaleAmount.Amount;
                            currencycode = calcdata.DayAliexpressSaleAmountStatistics.AmazonSaleAmount.CurrencyCode;
                            aeexport = calcdata.DayAliexpressSaleAmountStatistics.ExportAmount.Amount;
                            aezmdeclre = calcdata.DayAliexpressSaleAmountStatistics.ZMDeclaredAmount.Amount;
                        }
                        if (calcdata.DayAmazonSaleAmountStatistics != null && calcdata.DayAmazonSaleAmountStatistics.AmazonSaleAmount != null)
                        {
                            amazonDeclaredSalesAmount = calcdata.DayAmazonSaleAmountStatistics.AmazonSaleAmount.Amount;
                            currencycode = calcdata.DayAmazonSaleAmountStatistics.AmazonSaleAmount.CurrencyCode;
                            exportamount = calcdata.DayAmazonSaleAmountStatistics.ExportAmount.Amount;
                            zmdeclare = calcdata.DayAmazonSaleAmountStatistics.ZMDeclaredAmount.Amount;
                        }
                        if (calcdata.ArabAmazonSaleAmountStatistics != null && calcdata.ArabAmazonSaleAmountStatistics.AmazonSaleAmount != null)
                        {
                            amazonDeclaredSalesAmount = calcdata.ArabAmazonSaleAmountStatistics.AmazonSaleAmount.Amount;
                            currencycode = calcdata.ArabAmazonSaleAmountStatistics.AmazonSaleAmount.CurrencyCode;
                        }
                        var content = JsonConvert.SerializeObject(calcdata, Formatting.Indented, new JsonSerializerSettings
                        {
                            ContractResolver = new Newtonsoft.Json.Serialization.CamelCasePropertyNamesContractResolver()
                        });

                        CommonModel.VatPayableCalculateParam param = new CommonModel.VatPayableCalculateParam
                        {
                            TaxNo = order.TaxNumberInfo.TaxNumberEntity.VatNo,
                            TaxRateType = item.TaxRateType,
                            AmazonDeclaredSalesAmount = amazonDeclaredSalesAmount,
                            AmazonDeclaredSalesAmountExclTax = item.AmazonDeclaredSalesAmountExclTax,
                            AmazonDeclaredSalesAmount_OtherRate = amazonDeclaredSalesAmount_OtherRate,
                            OtherDeclaredSalesAmount = item.OtherDeclaredSalesAmount,
                            OtherDeclaredSalesAmountInclTax = item.OtherDeclaredSalesAmountInclTax,
                            OtherDeclaredSalesAmount_OtherRate = item.OtherDeclaredSalesAmount_OtherRate,
                            AmazonDeclaredSalesAmountAmtTax = item.AmazonDeclaredSalesAmountAmtTax,
                            EBayTotalPriceAmount = item.EBayTotalPriceAmount,
                            EBaySellerCollectedTaxAmount = item.EBaySellerCollectedTaxAmount,
                            EBaySoldForAmount = item.EBaySoldForAmount,
                            ExportAmount = exportamount,
                            OtherExportAmount = item.OtherExportAmount,
                            ZMDeclaredAmount = zmdeclare,
                            OtherZMDeclaredAmount = item.OtherZMDeclaredAmount,
                            AliexpressExportAmount = aeexport,
                            AliexpressSalesAmount = aesaleamount,
                            AliexpressZMDeclaredAmount = aezmdeclre,
                            OtherSaleTax = item.OtherSaleTax,
                            AmazonAmtTax = amazonAmtTax,
                            NativeB2BDeclaredAmount = nativeb2b,
                            OtherNativeB2BDeclaredAmount = item.OtherNativeB2BDeclaredAmount,
                            AliexpressNativeB2BDeclaredAmount = aenativeb2b,
                            ExpenseAmount = item.ExpenseAmount,
                            TaxPayAmount = item.TaxPayAmount,
                            ProductSalesAmount = item.ProductSalesAmount,
                            SellerTaxAmount = sellerTaxAmount,
                            ExportToEUAmount = exportToEUAmount,
                            ExportToNonEUAmount = exportToNonEUAmount,
                            IsTester = order.IsTester
                        };
                        // 2025英国亚马逊申报
                        if (calcdata.GBAmazonSaleAmountStatistics2025 != null)
                        {
                            var gbData = calcdata.GBAmazonSaleAmountStatistics2025;
                            param.GBAmazonSaleAmount = gbData.AmazonSaleAmount.Amount;
                            param.GBAmazonSaleTaxAmount = gbData.AmazonSaleTaxAmount.Amount;
                            param.GBMarketplaceTaxAmount = gbData.MarketplaceTaxAmount.Amount;
                            param.GBExportToEUAmount = gbData.ExportToEUAmount.Amount;
                            param.GBExportToNonEUAmount = gbData.ExportToNonEUAmount.Amount;
                            amazonDeclaredSalesAmount = gbData.AmazonSaleTaxAmount.Amount / 0.2M * 1.2M;
                        }
                        var declaredData = await _declaredCalculateService.ConvertToDeclaredData(taxnumber, param, item.DeclaredItems);
                        data = _typeAdapter.Adapt<Vat.Service.Domain.Common.Models.DeclaredData>(declaredData);
                        data.SetAutoCalcAmount(isGB ? autoCalculateDeclareSaleAmount : amazonDeclaredSalesAmount, param.AmazonDeclaredSalesAmount,
                            param.OtherDeclaredSalesAmount, param.AmazonDeclaredSalesAmount_OtherRate, param.OtherDeclaredSalesAmount_OtherRate, currencycode,
                            param.AmazonDeclaredSalesAmountExclTax, param.OtherDeclaredSalesAmountInclTax, param.OtherDeclaredSalesAmountLater, param.AmazonDeclaredSalesAmountAmtTax,
                            param.EBaySoldForAmount, param.EBayTotalPriceAmount, param.EBaySellerCollectedTaxAmount, param.ExportAmount, param.OtherExportAmount,
                            param.ZMDeclaredAmount, param.OtherZMDeclaredAmount, param.AliexpressSalesAmount, param.AliexpressExportAmount, param.AliexpressZMDeclaredAmount,
                            amazonAmtTax, item.OtherSaleTax, nativeb2b, item.OtherNativeB2BDeclaredAmount, aenativeb2b, item.ExpenseAmount, item.TaxPayAmount, item.ProductSalesAmount);
                        data.SetTotalDeclareSalesStatistic(content);
                    }
                    order.SubmitDeclare(item.DeclaredType, files, data, item.TaxRateType, item.ExistsVatNoCountry);

                    _dbContext.BeginTransaction();
                    await _declaredOrderRepository.UpdateAsync(order);
                    var user = _adminIdentityService.GetAdminUserIdentity();
                    try
                    {
                        var declareTypeDic = ConstantHelper.GetConstantInfoList(typeof(DeclareType)).ToDictionary(c => c.Value, c => c.Description);
                        var taxrateTypeDic = ConstantHelper.GetConstantInfoList(typeof(TaxRateType)).ToDictionary(c => c.Value, c => c.Description);
                        var logparam = new OperatorLogParam
                        {
                            OrderId = order.OrderId,
                            VatNo = order.TaxNumberInfo.TaxNumberEntity.VatNo,
                            BusinessType = OperatorLogType.Declared,
                            OperateTenant = new Vat.Service.Domain.Common.Models.Tenant(user.AdminUserId, user.FullName),
                            IpAddress = request.Ip,
                            OperateType = OperatorLogSubType.SubmitDeclaredOrder,
                            Description = string.Format(OperatorLogDesc.SubmitDeclaredOrder, declareTypeDic[item.DeclaredType], taxrateTypeDic[item.TaxRateType], data.AutoCalculateDeclareSaleAmount?.Amount),
                            TaxNumberEntity = order.TaxNumberInfo.TaxNumberEntity
                        };
                        await _operatorLogService.AddOrderOperatorLog(logparam);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError($"订单【{order.OrderId}】记录日志出现异常：{ex.Message}");
                    }
                    await _dbContext.CommitAsync();
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, $"订单【{item.OrderId}】提交申报出现异常：{ex.Message}");
                    errMsgs.Add($"订单【{item.OrderId}】提交申报出现异常：{ex.Message}");
                }
            }
            //异常提示
            if (errMsgs.Any())
            {
                string errmsg = string.Join(";", errMsgs);
                throw new VatCommonException(Error.Codes.BatchDeclareException, string.Format(Error.Names.BatchDeclareException, errmsg));
            }
            return true;
        }
    }
}

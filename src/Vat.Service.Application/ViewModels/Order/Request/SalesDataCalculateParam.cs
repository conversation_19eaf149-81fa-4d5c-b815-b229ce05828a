using System;
using System.Collections.Generic;
using System.Text;

namespace Vat.Service.Application.ViewModels.Order.Request
{
    public class SalesDataCalculateParam
    {
        /// <summary>
        /// 申报起始日期
        /// </summary>
        public DateTime StartDate { get; set; }
        /// <summary>
        /// 申报结束日期
        /// </summary>
        public DateTime EndDate { get; set; }
        /// <summary>
        /// 国家code
        /// </summary>
        public string CountryCode { get; set; }
        /// <summary>
        /// 文件路径
        /// </summary>
        public string FilePath { get; set; }
        /// <summary>
        /// ebay文件路径
        /// </summary>
        public string EBayFilePath { get; set; }
        /// <summary>
        /// Aliexpress文件路径
        /// </summary>
        public string AliexpressFilePath { get; set; }
        /// <summary>
        /// 税号
        /// </summary>
        public string TaxNo { get; set; }
        /// <summary>
        /// 已有vat帐号的国家
        /// </summary>
        public List<string> ExistVatCountryCodeList { get; set; }
        /// <summary>
        /// 税率类型（Frs）
        /// </summary>
        public string TaxRateType { get; set; }
        /// <summary>
        /// 其他平台销售税率
        /// </summary>
        public decimal OtherSaleTax { get; set; }
        /// <summary>
        /// 英国PVA递延 GBP
        /// </summary>
        public decimal Gbpva { get; set; }
    }
}
    
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using BFE.Framework.Infrastructure.Authorization.Admin;
using BFE.Framework.Infrastructure.Crosscutting.Helpers;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Vat.Service.AdminApi.Extensions;
using Vat.Service.Application.Commands.Order;
using Vat.Service.Application.Commands.Order.Handlers.RepairData;
using Vat.Service.Application.Commands.Order.RepairData;
using Vat.Service.Application.Commands.Tax;
using Vat.Service.Application.DTO.Common;
using Vat.Service.Application.Queries;
using Vat.Service.Application.ViewModels.Order;
using Vat.Service.Application.ViewModels.Order.Request;
using Vat.Service.Common.Model;
using CommonModel = Vat.Service.Common.Model;

namespace Vat.Service.AdminApi.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    // [Authorize]
    public class DeclaredOrderController : ControllerBase
    {
        private readonly IDeclaredOrderQueries _declaredOrderQueries;
        private readonly IMediator _mediator;

        public DeclaredOrderController(IDeclaredOrderQueries declaredOrderQueries, IMediator mediator)
        {
            _declaredOrderQueries = declaredOrderQueries;
            _mediator = mediator;
        }

        /// <summary>
        /// 分页
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost("page")]
        [Permission(PermissionCodes.DeclaredOrder.Page)]
        [ProducesResponseType(200, Type = typeof(PageResult<DeclaredOrderListModel>))]
        public async Task<IActionResult> GetDeclaredOrderList(DeclaredOrderParam request)
        {
            var result = await _declaredOrderQueries.GetDeclaredOrderInPage(request);
            return Ok(result);
        }
        /// <summary>
        /// 订单详情
        /// </summary>
        /// <param name="orderid"></param>
        /// <returns></returns>
        [HttpGet("{orderid}")]
        [Permission(PermissionCodes.DeclaredOrder.Detail)]
        [ProducesResponseType(200, Type = typeof(DeclaredOrderModel))]
        public async Task<IActionResult> GetDeclaredOrderDetail(string orderid)
        {
            var result = await _declaredOrderQueries.GetDeclaredOrderByOrderId(orderid);
            return Ok(result);
        }
        /// <summary>
        /// 已有税号国家
        /// </summary>
        /// <param name="orderid"></param>
        /// <returns></returns>
        [HttpGet("{orderid}/countries")]
        [ProducesResponseType(200, Type = typeof(List<string>))]
        public async Task<IActionResult> GetHasVatCountries(string orderid)
        {
            var result = await _declaredOrderQueries.GetHasVatCountries(orderid);
            return Ok(result);
        }
        /// <summary>
        /// 统计状态数量
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost("summary")]
        [Permission(PermissionCodes.DeclaredOrder.Page)]
        [ProducesResponseType(200, Type = typeof(PageResult<DeclaredOrderStatusAmountModel>))]
        public async Task<IActionResult> SummarizeStatusAmount(DeclaredOrderParam request)
        {
            var result = await _declaredOrderQueries.SummarizeStatusAmount(request);
            return Ok(result);
        }
        /// <summary>
        /// 年度申报详情
        /// </summary>
        /// <param name="orderid"></param>
        /// <returns></returns>
        [HttpGet("{orderid}/yearreport")]
        [Permission(PermissionCodes.DeclaredOrder.Page)]
        [ProducesResponseType(200, Type = typeof(YearReportOrderModel))]
        public async Task<IActionResult> GetYearReportOrder(string orderid)
        {
            var result = await _declaredOrderQueries.GetYearReportOrderByOrderId(orderid);
            return Ok(result);
        }
        /// <summary>
        /// 手动创建申报记录
        /// </summary>
        /// <param name="command"></param>
        /// <returns></returns>
        [HttpPost("declare/manual")]
        [Permission(PermissionCodes.TaxNumber.AddDeclareCycle)]
        public async Task<IActionResult> CreateDeclare(CreateDeclareCommand command)
        {
            var ip = HttpContext.GetClientUserIp();
            command.SetIpAdd(ip);

            var result = await _mediator.Send(command);
            return Ok(result);
        }
        /// <summary>
        /// 创建年报记录
        /// </summary>
        /// <param name="command"></param>
        /// <returns></returns>
        [HttpPost("declare/yearreport")]
        public async Task<IActionResult> CreateYearReport(CreateYearReportCommand command)
        {
            var result = await _mediator.Send(command);
            return Ok(result);
        }
        /// <summary>
        /// 年报异常提交
        /// </summary>
        /// <param name="command"></param>
        /// <returns></returns>
        [HttpPost("exception/yearreport")]
        [Permission(PermissionCodes.DeclaredOrder.Reject)]
        public async Task<IActionResult> YearReportException(YearReportExceptionCommand command)
        {
            var result = await _mediator.Send(command);
            return Ok(result);
        }

        /// <summary>
        /// 上传申报回执
        /// </summary>
        /// <param name="command"></param>
        /// <returns></returns>
        [HttpPut("upload/declaredResult")]
        [Permission(PermissionCodes.DeclaredOrder.UploadResult)]
        public async Task<IActionResult> UploadDeclaredResult(UploadDeclaredResultCommand command)
        {
            var result = await _mediator.Send(command);
            return Ok(result);
        }

        /// <summary>
        /// 上传申报回执通过年报
        /// </summary>
        /// <param name="command"></param>
        /// <returns></returns>
        [HttpPut("upload/declaredResultByYearResult")]
        [Permission(PermissionCodes.DeclaredOrder.UploadResult)]
        public async Task<IActionResult> UploadDeclaredResultByYearReport(UploadDeclaredResultByYearReportCommand command)
        {
            var result = await _mediator.Send(command);
            return Ok(result);
        }

        /// <summary>
        /// 上传支付水单
        /// </summary>
        /// <param name="command"></param>
        /// <returns></returns>
        [HttpPut("upload/payment")]
        [Permission(PermissionCodes.DeclaredOrder.UploadVatPaymentVoucher)]
        public async Task<IActionResult> UploadPayment(UploadPaymentCommand command)
        {
            var result = await _mediator.Send(command);
            return Ok(result);
        }
        /// <summary>
        /// 下载支付水单
        /// </summary>
        /// <param name="command"></param>
        /// <returns></returns>
        [HttpPut("download/payment")]
        [Permission(PermissionCodes.DeclaredOrder.DownloadVatPaymentVoucher)]
        public async Task<IActionResult> DownloadPayment(DownloadPaymentCommand command)
        {
            var result = await _mediator.Send(command);
            return Ok(result);
        }
        /// <summary>
        /// 上传缴税回执
        /// </summary>
        /// <param name="command"></param>
        /// <returns></returns>
        [HttpPut("upload/taxpaymentReceipt")]
        [Permission(PermissionCodes.DeclaredOrder.UploadTaxPaymentReceipt)]
        public async Task<IActionResult> UploadTaxPaymentReceipt(UploadTaxPaymentReceiptCommand command)
        {
            var result = await _mediator.Send(command);
            return Ok(result);
        }
        /// <summary>
        /// 下载缴税回执
        /// </summary>
        /// <param name="command"></param>
        /// <returns></returns>
        [HttpPut("download/taxpaymentReceipt")]
        //[Permission(PermissionCodes.DeclaredOrder.DownloadVatPaymentVoucher)]
        public async Task<IActionResult> DownloadtaxpaymentReceipt(DownloadTaxPaymentReceiptCommand command)
        {
            var result = await _mediator.Send(command);
            return Ok(result);
        }

        /// <summary>
        /// 获取上传申报回执的表单数据
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        [HttpGet("list/batch/declaredReceiptList")]
        public async Task<IActionResult> GetBatchUploadDeclaredReceiptList([FromQuery] List<string> ids)
        {
            var result = await _declaredOrderQueries.GetUploadDeclaredReceiptList(ids);
            return Ok(result);
        }

        /// <summary>
        /// 检查确认系统解析批量上传申报回执和缴税回执压缩包
        /// </summary>
        /// <param name="command"></param>
        /// <returns></returns>
        [HttpPut("check/batch/zip")]
        [Permission(PermissionCodes.DeclaredOrder.UploadResult)]
        public async Task<IActionResult> CheckBatchUploadZip(CheckBatchUploadZipCommand command)
        {
            var result = await _mediator.Send(command);
            return Ok(result);
        }

        /// <summary>
        /// 批量上传申报结果（新）这个接口不适用英国，波兰，奥地利
        /// </summary>
        /// <param name="command"></param>
        /// <returns></returns>
        [HttpPut("upload/batch/declaredReceiptNew")]
        [Permission(PermissionCodes.DeclaredOrder.UploadResult)]
        public async Task<IActionResult> BatchUploadDeclaredReceiptNew(BatchUploadDeclaredReceiptByZipCommand command)
        {
            var result = await _mediator.Send(command);
            return Ok(result);
        }

        /// <summary>
        /// 批量上传缴税回执（新）
        /// </summary>
        /// <param name="command"></param>
        /// <returns></returns>
        [HttpPut("upload/batch/paymentReceiptNew")]
        [Permission(PermissionCodes.DeclaredOrder.UploadResult)]
        public async Task<IActionResult> BatchUploadPaymentReceiptNew(BatchUploadTaxPaymentReceiptByZipCommand command)
        {
            var result = await _mediator.Send(command);
            return Ok(result);
        }

        /// <summary>
        /// 批量上传申报结果  适用于所有国家
        /// </summary>
        /// <param name="command"></param>
        /// <returns></returns>
        [HttpPut("upload/batch/declaredReceipt")]
        [Permission(PermissionCodes.DeclaredOrder.UploadResult)]
        public async Task<IActionResult> BatchUploadDeclaredReceipt(BatchUploadDeclaredResultCommand command)
        {
            var result = await _mediator.Send(command);
            return Ok(result);
        }

        /// <summary>
        /// 批量上传申报结果（识别压缩包里的文件名）适用于英国
        /// </summary>
        /// <returns></returns>
        [HttpPut("upload/batch/BatchUploadDeclaredReceiptIdentifyFileName")]
        public async Task<IActionResult> BatchUploadDeclaredReceiptIdentifyFileName([FromBody] BatchUploadDeclaredReceiptIdentifyFileNameCommand command)
        {
            var result = await _mediator.Send(command);
            return Ok(result);
        }
        /// <summary>
        /// 批量上传缴税回执
        /// </summary>
        /// <param name="command"></param>
        /// <returns></returns>
        [HttpPut("upload/batch/taxpaymentReceipt")]
        [Permission(PermissionCodes.DeclaredOrder.UploadTaxPaymentReceipt)]
        public async Task<IActionResult> BatchUploadTaxPaymentReceipt(BatchUploadTaxPaymentReceiptCommand command)
        {
            var result = await _mediator.Send(command);
            return Ok(result);
        }

        /// <summary>
        /// 批量支付水单
        /// </summary>
        /// <param name="command"></param>
        /// <returns></returns>
        [HttpPut("upload/batch/payment")]
        [Permission(PermissionCodes.DeclaredOrder.UploadVatPaymentVoucher)]
        public async Task<IActionResult> BatchUploadPayment(BatchUploadPaymentCommand command)
        {
            var result = await _mediator.Send(command);
            return Ok(result);
        }
        /// <summary>
        /// 审核订单
        /// </summary>
        [HttpPut("audit/{orderid}")]
        //[Permission(PermissionCodes.DeclaredOrder.AuditOrder)]
        public async Task<IActionResult> AuditOrder(string orderid, AuditDeclaredOrderCommand param)
        {
            var ip = HttpContext.GetClientUserIp();
            param.SetIp(ip);
            param.SetOrderId(orderid);
            var result = await _mediator.Send(param);

            return Ok(result);
        }
        /// <summary>
        /// 支付订单
        /// </summary>
        [HttpPut("pay/{orderid}")]
        //[Permission(PermissionCodes.DeclaredOrder.payOrder)]
        public async Task<IActionResult> PayOrder(string orderid, PayDeclaredOrderCommand param)
        {
            var ip = HttpContext.GetClientUserIp();
            param.SetIp(ip);
            param.SetOrderId(orderid);
            var result = await _mediator.Send(param);

            return Ok(result);
        }
        /// <summary>
        /// 提交税代
        /// </summary>
        /// <param name="command"></param>
        /// <returns></returns>
        [HttpPut("submitagent")]
        [Permission(PermissionCodes.DeclaredOrder.SubmitAgent)]
        public async Task<IActionResult> SubmitAgent(SubmitAgentCommand command)
        {
            var result = await _mediator.Send(command);
            return Ok(result);
        }
        /// <summary>
        /// 发送提醒通知
        /// </summary>
        /// <param name="command"></param>
        /// <returns></returns>
        [HttpPut("notify")]
        [Permission(PermissionCodes.DeclaredOrder.SendNotify)]
        public async Task<IActionResult> SendNotify(SendNotifyCommand command)
        {
            var ip = HttpContext.GetClientUserIp();
            command.SetIp(ip);

            var result = await _mediator.Send(command);
            return Ok(result);
        }
        #region 申报提交
        /// <summary>
        /// 提交待申报记录（异常类型）
        /// </summary>
        /// <param name="command"></param>
        /// <returns></returns>
        [HttpPut("submit")]
        [Permission(PermissionCodes.DeclaredOrder.Reject)]
        public async Task<IActionResult> SubmitDeclaredOrder(SubmitDeclaredOrderCommand command)
        {
            var ip = HttpContext.GetClientUserIp();
            command.SetIp(ip);

            var result = await _mediator.Send(command);
            return Ok(result);
        }
        /// <summary>
        /// 批量提交待申报记录
        /// </summary>
        /// <param name="command"></param>
        /// <returns></returns>
        [HttpPost("submit/batch")]
        //[Permission(PermissionCodes.DeclaredOrder.BatchDeclare)]
        public async Task<IActionResult> BatchSubmitDeclaredOrder(BatchSubmitDeclaredOrderCommand command)
        {
            var ip = HttpContext.GetClientUserIp();
            command.SetIp(ip);

            var result = await _mediator.Send(command);
            return Ok(result);
        }
        /// <summary>
        /// 销售数据文件计算
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost("salesdatacalculate")]
        public async Task<IActionResult> SalesDataCalculate([FromBody] SalesDataCalculateParam param)
        {
            var result = await _declaredOrderQueries.SalesDataCalculate(param);
            return Ok(result);
        }
        /// <summary>
        /// amazon销售数据文件计算
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost("salesdatacalculate/amazon")]
        public async Task<IActionResult> AmazonSalesDataCalculate([FromBody] SalesDataCalculateParam param)
        {
            var result = await _declaredOrderQueries.AmazonSalesDataCalculate(param);
            return Ok(result);
        }
        /// <summary>
        /// ebay销售数据文件计算
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost("salesdatacalculate/ebay")]
        public async Task<IActionResult> EBaySalesDataCalculate([FromBody] SalesDataCalculateParam param)
        {
            var result = await _declaredOrderQueries.EBaySalesDataCalculate(param);
            return Ok(result);
        }
        /// <summary>
        /// 阿拉伯国家发票金额计算
        /// </summary>
        /// <param name="param">发票路径</param>
        /// <returns></returns>
        [HttpPost("salesdatacalculate/arab/invoiceAmount")]
        [ProducesResponseType(200, Type = typeof(decimal))]
        public async Task<IActionResult> ArabInvoiceCalculate([FromBody] CommonModel.ArabInvoiceParam param)
        {
            var result = await _declaredOrderQueries.GetInvoiceAmount(param);
            return Ok(result);
        }
        /// <summary>
        /// 应缴税额计算
        /// </summary>
        /// <param name="param"></param>
        /// 返回结果：TotalSalesAmount：申报销售总额（含税）,SaleFreeTaxAmount:申报销售总额（不含税）,SaleVatAmount:销售税,ImportGoodsAmount:进口增值税,TaxDue:应缴税额
        /// <returns></returns>
        [HttpPost("taxdueCalculate")]
        public async Task<IActionResult> VatPayableCalculate([FromBody] CommonModel.VatPayableCalculateParam param)
        {
            var result = await _declaredOrderQueries.VatDueCalculate(param);
            return Ok(result);
        }
        /// <summary>
        /// 下载资料包
        /// </summary>
        /// <param name="orderids"></param>
        /// <returns></returns>
        [HttpPost("downloadPackage")]
        [Permission(PermissionCodes.DeclaredOrder.DownLoad)]
        public async Task<FileResult> DownloadPackage([FromForm] string oids)
        {
            List<string> orderids = oids.Split(",").Where(i => !i.IsNullOrBlank()).ToList();
            var stream = await _declaredOrderQueries.GetDeclaredDataPackageByOrderId(orderids);
            var name = "申报资料包";
            if (orderids.Count == 1)
            {
                var order = await _declaredOrderQueries.GetDeclaredOrderByOrderId(orderids[0]);
                name = $"{order.AgentManagerId}_{order.CnName}_{order.DeclarationCycle.StartTime.Value.ToString("yyyy-MM-dd")}--{order.DeclarationCycle.EndTime.Value.ToString("yyyy-MM-dd")}";
            }
            string fileName = string.Format("{1}_{0}.zip", DateTime.Now.ToString("yyyyMMdd"), name);
            var result = File(stream, "application/zip; charset=utf-8", fileName);

            return result;
        }

        /// <summary>
        /// 导出excel
        /// </summary>
        [HttpPost("export")]
        [Permission(PermissionCodes.DeclaredOrder.Export)]
        [ProducesResponseType(200, Type = typeof(FileResult))]
        public async Task<FileResult> ExportOrders([FromBody] DeclaredOrderParam request)
        {
            var stream = await _declaredOrderQueries.ExportOrders(request);

            string fileName = string.Format("vatExportDeclaredOrder_{0}.xlsx", DateTime.Now.ToString("yyyyMMddHHss"));
            var result = File(stream, "application/vnd.ms-excel", fileName);

            return result;
        }

        #endregion
        /// <summary>
        /// 申报订单取消
        /// </summary>
        /// <param name="command"></param>
        /// <returns></returns>
        [HttpPost("remove")]
        [Permission(PermissionCodes.DeclaredOrder.Cancel)]
        public async Task<IActionResult> CancelDeclaredOrder(CancelDeclaredOrderCommand command)
        {
            var ip = HttpContext.GetClientUserIp();
            command.SetIpAdd(ip);

            var result = await _mediator.Send(command);
            return Ok(result);
        }
        /// <summary>
        /// 查询未完成的待申报
        /// </summary>
        /// <param name="vatno"></param>
        /// <returns></returns>
        [HttpGet("unfinished")]
        [Permission(PermissionCodes.DeclaredOrder.Page)]
        [ProducesResponseType(200, Type = typeof(List<UnFinishedDeclare>))]
        public async Task<IActionResult> GetUnFinishedDeclared(string vatno)
        {
            var result = await _declaredOrderQueries.GetUnFinishedDeclared(vatno);
            return Ok(result);
        }

        /// <summary>
        /// 生成发票
        /// </summary>
        /// <returns></returns>
        [HttpPut("pl/invoice")]
        //[Permission(PermissionCodes.DeclaredOrder.Page)]
        [ProducesResponseType(200, Type = typeof(bool))]
        public async Task<IActionResult> GetUnFinishedDeclared(GeneratePLInoviceCommand command)
        {
            var result = await _mediator.Send(command);
            return Ok(result);
        }
        /// <summary>
        /// 业务删除申报订单
        /// </summary>
        [HttpPut("manager/remove")]
        [Permission(PermissionCodes.DeclaredOrder.Detail)]
        public async Task<IActionResult> ManagerDeleteOrder(ManagerDeleteDeclaredOrderCommand command)
        {
            var ip = HttpContext.GetClientUserIp();
            command.Ip = ip;
            var result = await _mediator.Send(command);

            return Ok(result);
        }
        #region 初始化数据
        /// <summary>
        /// 初始化抵扣项code
        /// </summary>
        /// <param name="command"></param>
        /// <returns></returns>
        [HttpPost("deductioncode/init")]
        public async Task<IActionResult> InitBusinessField(InitDeductionCodeCommand command)
        {
            var result = await _mediator.Send(command);
            return Ok(result);
        }
        /// <summary>
        /// 抵扣项code
        /// </summary>
        /// <param name="orderid"></param>
        /// <returns></returns>
        [HttpGet("deductioncode")]
        [ProducesResponseType(200, Type = typeof(List<DeductionCodeModel>))]
        public async Task<IActionResult> GetDeductionCodes()
        {
            var result = await _declaredOrderQueries.GetDeductionCodes();
            return Ok(result);
        }
        #endregion

        #region 数据修复
        /// <summary>
        /// 申报数据迁移
        /// </summary>
        /// <param name="command"></param>
        /// <returns></returns>
        [HttpPost("migration")]
        public async Task<IActionResult> SendNotify(DeclaredDataMigrationCommand command)
        {
            var result = await _mediator.Send(command);
            return Ok(result);
        }

        /// <summary>
        /// 待申报数据修复
        /// </summary>
        /// <param name="command"></param>
        /// <returns></returns>
        [HttpPost("repair/todeclared")]
        public async Task<IActionResult> DeleteTaxNumber(ToDeclareOrderGenerateRepairCommand command)
        {
            var result = await _mediator.Send(command);
            return Ok(result);
        }
        /// <summary>
        /// 修复订单状态
        /// </summary>
        /// <param name="command"></param>
        /// <returns></returns>
        [HttpPut("repair/status1")]
        public async Task<IActionResult> RepairOrderStatus(RepairDeclaredOrderStatusCommand command)
        {
            var result = await _mediator.Send(command);
            return Ok(result);
        }
        /// <summary>
        /// 更新订单状态待申报
        /// </summary>
        /// <param name="command"></param>
        /// <returns></returns>
        [HttpPut("repair/status/todeclare")]
        public async Task<IActionResult> CancelOrderStatus(CancelOrderStatusCommand command)
        {
            var result = await _mediator.Send(command);
            return Ok(result);
        }
        /// <summary>
        /// 修复申报截止日期
        /// </summary>
        /// <param name="command"></param>
        /// <returns></returns>
        [HttpPut("repair/deadline")]
        public async Task<IActionResult> RepairDeclaredOrderDeadline(RepairDeclaredOrderDeadlineCommand command)
        {
            var result = await _mediator.Send(command);
            return Ok(result);
        }
        /// <summary>
        /// 修复申报订单税代配置
        /// </summary>
        /// <param name="command"></param>
        /// <returns></returns>
        [HttpPut("repair/agentconfig")]
        public async Task<IActionResult> RepairDeclaredOrderAgentConfigItem(RepairDeclaredOrderAgentConfigItemCommand command)
        {
            var result = await _mediator.Send(command);
            return Ok(result);
        }

        /// <summary>
        /// 检查重复的订单
        /// </summary>
        /// <param name="command"></param>
        /// <returns></returns>
        [HttpPost("test/checkrepeatorder")]
        public async Task<IActionResult> CheckRepeatDeclareOrder(CheckRepeatDeclareOrderCommand command)
        {
            var result = await _mediator.Send(command);
            return Ok(result);
        }

        /// <summary>
        /// 更新申报数据内容
        /// </summary>
        /// <param name="command"></param>
        /// <returns></returns>
        [HttpPut("repair/declaredDataContent")]
        public async Task<IActionResult> RepairDeclaredDataContent(RepairDeclaredDataContentCommand command)
        {
            var result = await _mediator.Send(command);
            return Ok(result);
        }
        /// <summary>
        /// 修复转换过程申报文件丢失
        /// </summary>
        /// <param name="command"></param>
        /// <returns></returns>
        [HttpPut("repair/declaredfile")]
        public async Task<IActionResult> RepairDeclaredFile(RepairDeclaredFileCommand command)
        {
            var result = await _mediator.Send(command);
            return Ok(result);
        }

        /// <summary>
        /// 检查待申报历史记录
        /// </summary>
        /// <param name="command"></param>
        /// <returns></returns>
        //[HttpPost("repair/todeclare")]
        //public async Task<IActionResult> RepairDeclaredPending(RepairDeclaredPendingCommand command)
        //{
        //    var result = await _mediator.Send(command);
        //    return Ok(result);
        //}
        /// <summary>
        /// 检查待申报历史记录
        /// </summary>
        /// <param name = "command" ></param>
        /// <returns></returns>
        [HttpPost("check/repeatdeclare")]
        public async Task<IActionResult> CheckDeclareOrderRepeat(CheckDeclareOrderRepeatCommand command)
        {
            var result = await _mediator.Send(command);
            return Ok(result);
        }
        /// <summary>
        /// 修复申报订单数据
        /// </summary>
        /// <param name = "command" ></param>
        /// <returns></returns>
        [HttpPost("repair/orderinfo2")]
        public async Task<IActionResult> RepairDeclaredOrderInfo(RepairDeclaredOrderInfoCommand command)
        {
            var result = await _mediator.Send(command);
            return Ok(result);
        }
        /// <summary>
        /// 删除申报月份
        /// </summary>
        /// <param name="command"></param>
        /// <returns></returns>
        [HttpDelete("remove/declaremonth")]
        public async Task<IActionResult> DeleteDeclareMonth(DeleteDeclareMonthCommand command)
        {
            var result = await _mediator.Send(command);
            return Ok(result);
        }
        /// <summary>
        /// 标记已发送通知
        /// </summary>
        /// <param name="command"></param>
        /// <returns></returns>
        [HttpPut("repair/notify")]
        public async Task<IActionResult> UpdateDeclaredOrderNotifyStatus(UpdateDeclaredOrderNotifyStatusCommand command)
        {
            var result = await _mediator.Send(command);
            return Ok(result);
        }
        /// <summary>
        /// 销售总额更新
        /// </summary>
        /// <param name="command"></param>
        /// <returns></returns>
        [HttpPut("repair/declaredLetter")]
        public async Task<IActionResult> RepairDeclaredData(RepairDeclaredDataCommand command)
        {
            var result = await _mediator.Send(command);
            return Ok(result);
        }
        /// <summary>
        /// 申报订单删除
        /// </summary>
        /// <param name="command"></param>
        /// <returns></returns>
        [HttpDelete("repair/deletedeclaredorder1")]
        public async Task<IActionResult> DeleteDeclaredOrder(DeleteDeclaredOrderCommand command)
        {
            var result = await _mediator.Send(command);
            return Ok(result);
        }

        /// <summary>
        /// 修复拆分销售总额
        /// </summary>
        /// <param name="command"></param>
        /// <returns></returns>
        [HttpPut("repair/RepairSplitSaleAmount")]
        public async Task<IActionResult> RepairSplitSaleAmount(RepairSplitSaleAmountCommand command)
        {
            var result = await _mediator.Send(command);
            return Ok(result);
        }
        /// <summary>
        /// 添加申报月份
        /// </summary>
        /// <param name="command"></param>
        /// <returns></returns>
        [HttpPost("repair/declaredmonth")]
        public async Task<IActionResult> AddDeclaredMonth(AddDeclaredMonthCommand command)
        {
            var result = await _mediator.Send(command);
            return Ok(result);
        }
        /// <summary>
        /// 更新申报信
        /// </summary>
        /// <param name="command"></param>
        /// <returns></returns>
        [HttpPut("repair/systemdeclaredFile")]
        public async Task<IActionResult> GenerateDeclaredLetter(GenerateDeclaredLetterCommand command)
        {
            var result = await _mediator.Send(command);
            return Ok(result);
        }
        /// <summary>
        /// 更新申报计算结果
        /// </summary>
        /// <param name="command"></param>
        /// <returns></returns>
        [HttpPut("repair/calcresult")]
        public async Task<IActionResult> RepairDeclaredCalcData(RepairDeclaredCalcDataCommand command)
        {
            var result = await _mediator.Send(command);
            return Ok(result);
        }
        /// <summary>
        /// 更新申报订单发票状态
        /// </summary>
        /// <param name="command"></param>
        /// <returns></returns>
        [HttpPut("repair/invoicestatus")]
        public async Task<IActionResult> RepairOrderInvoiceStatus(RepairOrderInvoiceStatusCommand command)
        {
            var result = await _mediator.Send(command);
            return Ok(result);
        }
        /// <summary>
        /// 修复速卖通销售数据文件
        /// </summary>
        /// <param name="command"></param>
        /// <returns></returns>
        [HttpPut("repair/aliexpressfile")]
        public async Task<IActionResult> RepairAliexpressFile(RepairAliexpressFileCommand command)
        {
            var result = await _mediator.Send(command);
            return Ok(result);
        }
        /// <summary>
        /// 修复申报订单vatno税号
        /// </summary>
        /// <param name="command"></param>
        /// <returns></returns>
        [HttpPut("repair/taxno")]
        public async Task<IActionResult> RepairDeclaredOrderTaxno(RepairDeclaredOrderTaxnoCommand command)
        {
            var result = await _mediator.Send(command);
            return Ok(result);
        }

        /// <summary>
        /// 修复申报回执压缩包
        /// </summary>
        /// <param name="command"></param>
        /// <returns></returns>
        [HttpPut("repair/DeclaredReceiptZip")]
        public async Task<IActionResult> RepairDeclaredReceiptZip(RepairDeclaredReceiptZipCommand command)
        {
            var result = await _mediator.Send(command);
            return Ok(result);
        }
        /// <summary>
        /// 上传其他文件
        /// </summary>
        /// <param name="command"></param>
        /// <returns></returns>
        [HttpPut("otherfile")]
        public async Task<IActionResult> UploadOtherFile([FromForm] RepairDeclaredOrderOtherFileCommand command)
        {
            var result = await _mediator.Send(command);
            return Ok(result);
        }
        #endregion
    }
}